"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[1786],{81786:(t,e,s)=>{s.d(e,{d:()=>J,p:()=>r,s:()=>Z});var n=s(1056);var i=s(76235);var u=function(){var t=function(t,e,s,n){for(s=s||{},n=t.length;n--;s[t[n]]=e);return s},e=[1,17],s=[1,18],n=[1,19],i=[1,39],u=[1,40],r=[1,25],a=[1,23],c=[1,24],o=[1,31],l=[1,32],h=[1,33],A=[1,34],p=[1,35],f=[1,36],y=[1,26],d=[1,27],E=[1,28],C=[1,29],b=[1,43],m=[1,30],k=[1,42],T=[1,44],F=[1,41],g=[1,45],B=[1,9],D=[1,8,9],_=[1,56],S=[1,57],N=[1,58],L=[1,59],v=[1,60],$=[1,61],O=[1,62],I=[1,8,9,39],x=[1,74],R=[1,8,9,12,13,21,37,39,42,59,60,61,62,63,64,65,70,72],w=[1,8,9,12,13,19,21,37,39,42,46,59,60,61,62,63,64,65,70,72,74,80,95,97,98],P=[13,74,80,95,97,98],M=[13,64,65,74,80,95,97,98],G=[13,59,60,61,62,63,74,80,95,97,98],U=[1,93],z=[1,110],K=[1,108],Y=[1,102],j=[1,103],Q=[1,104],X=[1,105],W=[1,106],q=[1,107],H=[1,109],J=[1,8,9,37,39,42],V=[1,8,9,21],Z=[1,8,9,78],tt=[1,8,9,21,73,74,78,80,81,82,83,84,85];var et={trace:function t(){},yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,className:17,classLiteralName:18,GENERICTYPE:19,relationStatement:20,LABEL:21,namespaceStatement:22,classStatement:23,memberStatement:24,annotationStatement:25,clickStatement:26,styleStatement:27,cssClassStatement:28,noteStatement:29,direction:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,namespaceIdentifier:36,STRUCT_START:37,classStatements:38,STRUCT_STOP:39,NAMESPACE:40,classIdentifier:41,STYLE_SEPARATOR:42,members:43,CLASS:44,ANNOTATION_START:45,ANNOTATION_END:46,MEMBER:47,SEPARATOR:48,relation:49,NOTE_FOR:50,noteText:51,NOTE:52,direction_tb:53,direction_bt:54,direction_rl:55,direction_lr:56,relationType:57,lineType:58,AGGREGATION:59,EXTENSION:60,COMPOSITION:61,DEPENDENCY:62,LOLLIPOP:63,LINE:64,DOTTED_LINE:65,CALLBACK:66,LINK:67,LINK_TARGET:68,CLICK:69,CALLBACK_NAME:70,CALLBACK_ARGS:71,HREF:72,STYLE:73,ALPHA:74,stylesOpt:75,CSSCLASS:76,style:77,COMMA:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,commentToken:86,textToken:87,graphCodeTokens:88,textNoTagsToken:89,TAGSTART:90,TAGEND:91,"==":92,"--":93,DEFAULT:94,MINUS:95,keywords:96,UNICODE_TEXT:97,BQUOTE_STR:98,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",19:"GENERICTYPE",21:"LABEL",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",37:"STRUCT_START",39:"STRUCT_STOP",40:"NAMESPACE",42:"STYLE_SEPARATOR",44:"CLASS",45:"ANNOTATION_START",46:"ANNOTATION_END",47:"MEMBER",48:"SEPARATOR",50:"NOTE_FOR",52:"NOTE",53:"direction_tb",54:"direction_bt",55:"direction_rl",56:"direction_lr",59:"AGGREGATION",60:"EXTENSION",61:"COMPOSITION",62:"DEPENDENCY",63:"LOLLIPOP",64:"LINE",65:"DOTTED_LINE",66:"CALLBACK",67:"LINK",68:"LINK_TARGET",69:"CLICK",70:"CALLBACK_NAME",71:"CALLBACK_ARGS",72:"HREF",73:"STYLE",74:"ALPHA",76:"CSSCLASS",78:"COMMA",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",88:"graphCodeTokens",90:"TAGSTART",91:"TAGEND",92:"==",93:"--",94:"DEFAULT",95:"MINUS",96:"keywords",97:"UNICODE_TEXT",98:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,2],[17,1],[17,1],[17,2],[17,2],[17,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[22,4],[22,5],[36,2],[38,1],[38,2],[38,3],[23,1],[23,3],[23,4],[23,6],[41,2],[41,3],[25,4],[43,1],[43,2],[24,1],[24,2],[24,1],[24,1],[20,3],[20,4],[20,4],[20,5],[29,3],[29,2],[30,1],[30,1],[30,1],[30,1],[49,3],[49,2],[49,2],[49,1],[57,1],[57,1],[57,1],[57,1],[57,1],[58,1],[58,1],[26,3],[26,4],[26,3],[26,4],[26,4],[26,5],[26,3],[26,4],[26,4],[26,5],[26,4],[26,5],[26,5],[26,6],[27,3],[28,3],[75,1],[75,3],[77,1],[77,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[86,1],[86,1],[87,1],[87,1],[87,1],[87,1],[87,1],[87,1],[87,1],[89,1],[89,1],[89,1],[89,1],[16,1],[16,1],[16,1],[16,1],[18,1],[51,1]],performAction:function t(e,s,n,i,u,r,a){var c=r.length-1;switch(u){case 8:this.$=r[c-1];break;case 9:case 11:case 12:this.$=r[c];break;case 10:case 13:this.$=r[c-1]+r[c];break;case 14:case 15:this.$=r[c-1]+"~"+r[c]+"~";break;case 16:i.addRelation(r[c]);break;case 17:r[c-1].title=i.cleanupLabel(r[c]);i.addRelation(r[c-1]);break;case 27:this.$=r[c].trim();i.setAccTitle(this.$);break;case 28:case 29:this.$=r[c].trim();i.setAccDescription(this.$);break;case 30:i.addClassesToNamespace(r[c-3],r[c-1]);break;case 31:i.addClassesToNamespace(r[c-4],r[c-1]);break;case 32:this.$=r[c];i.addNamespace(r[c]);break;case 33:this.$=[r[c]];break;case 34:this.$=[r[c-1]];break;case 35:r[c].unshift(r[c-2]);this.$=r[c];break;case 37:i.setCssClass(r[c-2],r[c]);break;case 38:i.addMembers(r[c-3],r[c-1]);break;case 39:i.setCssClass(r[c-5],r[c-3]);i.addMembers(r[c-5],r[c-1]);break;case 40:this.$=r[c];i.addClass(r[c]);break;case 41:this.$=r[c-1];i.addClass(r[c-1]);i.setClassLabel(r[c-1],r[c]);break;case 42:i.addAnnotation(r[c],r[c-2]);break;case 43:this.$=[r[c]];break;case 44:r[c].push(r[c-1]);this.$=r[c];break;case 45:break;case 46:i.addMember(r[c-1],i.cleanupLabel(r[c]));break;case 47:break;case 48:break;case 49:this.$={id1:r[c-2],id2:r[c],relation:r[c-1],relationTitle1:"none",relationTitle2:"none"};break;case 50:this.$={id1:r[c-3],id2:r[c],relation:r[c-1],relationTitle1:r[c-2],relationTitle2:"none"};break;case 51:this.$={id1:r[c-3],id2:r[c],relation:r[c-2],relationTitle1:"none",relationTitle2:r[c-1]};break;case 52:this.$={id1:r[c-4],id2:r[c],relation:r[c-2],relationTitle1:r[c-3],relationTitle2:r[c-1]};break;case 53:i.addNote(r[c],r[c-1]);break;case 54:i.addNote(r[c]);break;case 55:i.setDirection("TB");break;case 56:i.setDirection("BT");break;case 57:i.setDirection("RL");break;case 58:i.setDirection("LR");break;case 59:this.$={type1:r[c-2],type2:r[c],lineType:r[c-1]};break;case 60:this.$={type1:"none",type2:r[c],lineType:r[c-1]};break;case 61:this.$={type1:r[c-1],type2:"none",lineType:r[c]};break;case 62:this.$={type1:"none",type2:"none",lineType:r[c]};break;case 63:this.$=i.relationType.AGGREGATION;break;case 64:this.$=i.relationType.EXTENSION;break;case 65:this.$=i.relationType.COMPOSITION;break;case 66:this.$=i.relationType.DEPENDENCY;break;case 67:this.$=i.relationType.LOLLIPOP;break;case 68:this.$=i.lineType.LINE;break;case 69:this.$=i.lineType.DOTTED_LINE;break;case 70:case 76:this.$=r[c-2];i.setClickEvent(r[c-1],r[c]);break;case 71:case 77:this.$=r[c-3];i.setClickEvent(r[c-2],r[c-1]);i.setTooltip(r[c-2],r[c]);break;case 72:this.$=r[c-2];i.setLink(r[c-1],r[c]);break;case 73:this.$=r[c-3];i.setLink(r[c-2],r[c-1],r[c]);break;case 74:this.$=r[c-3];i.setLink(r[c-2],r[c-1]);i.setTooltip(r[c-2],r[c]);break;case 75:this.$=r[c-4];i.setLink(r[c-3],r[c-2],r[c]);i.setTooltip(r[c-3],r[c-1]);break;case 78:this.$=r[c-3];i.setClickEvent(r[c-2],r[c-1],r[c]);break;case 79:this.$=r[c-4];i.setClickEvent(r[c-3],r[c-2],r[c-1]);i.setTooltip(r[c-3],r[c]);break;case 80:this.$=r[c-3];i.setLink(r[c-2],r[c]);break;case 81:this.$=r[c-4];i.setLink(r[c-3],r[c-1],r[c]);break;case 82:this.$=r[c-4];i.setLink(r[c-3],r[c-1]);i.setTooltip(r[c-3],r[c]);break;case 83:this.$=r[c-5];i.setLink(r[c-4],r[c-2],r[c]);i.setTooltip(r[c-4],r[c-1]);break;case 84:this.$=r[c-2];i.setCssStyle(r[c-1],r[c]);break;case 85:i.setCssClass(r[c-1],r[c]);break;case 86:this.$=[r[c]];break;case 87:r[c-2].push(r[c]);this.$=r[c-2];break;case 89:this.$=r[c-1]+r[c];break}},table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:37,17:20,18:38,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,31:e,33:s,35:n,36:21,40:i,41:22,44:u,45:r,47:a,48:c,50:o,52:l,53:h,54:A,55:p,56:f,66:y,67:d,69:E,73:C,74:b,76:m,80:k,95:T,97:F,98:g},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},t(B,[2,5],{8:[1,46]}),{8:[1,47]},t(D,[2,16],{21:[1,48]}),t(D,[2,18]),t(D,[2,19]),t(D,[2,20]),t(D,[2,21]),t(D,[2,22]),t(D,[2,23]),t(D,[2,24]),t(D,[2,25]),t(D,[2,26]),{32:[1,49]},{34:[1,50]},t(D,[2,29]),t(D,[2,45],{49:51,57:54,58:55,13:[1,52],21:[1,53],59:_,60:S,61:N,62:L,63:v,64:$,65:O}),{37:[1,63]},t(I,[2,36],{37:[1,65],42:[1,64]}),t(D,[2,47]),t(D,[2,48]),{16:66,74:b,80:k,95:T,97:F},{16:37,17:67,18:38,74:b,80:k,95:T,97:F,98:g},{16:37,17:68,18:38,74:b,80:k,95:T,97:F,98:g},{16:37,17:69,18:38,74:b,80:k,95:T,97:F,98:g},{74:[1,70]},{13:[1,71]},{16:37,17:72,18:38,74:b,80:k,95:T,97:F,98:g},{13:x,51:73},t(D,[2,55]),t(D,[2,56]),t(D,[2,57]),t(D,[2,58]),t(R,[2,11],{16:37,18:38,17:75,19:[1,76],74:b,80:k,95:T,97:F,98:g}),t(R,[2,12],{19:[1,77]}),{15:78,16:79,74:b,80:k,95:T,97:F},{16:37,17:80,18:38,74:b,80:k,95:T,97:F,98:g},t(w,[2,112]),t(w,[2,113]),t(w,[2,114]),t(w,[2,115]),t([1,8,9,12,13,19,21,37,39,42,59,60,61,62,63,64,65,70,72],[2,116]),t(B,[2,6],{10:5,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,17:20,36:21,41:22,16:37,18:38,5:81,31:e,33:s,35:n,40:i,44:u,45:r,47:a,48:c,50:o,52:l,53:h,54:A,55:p,56:f,66:y,67:d,69:E,73:C,74:b,76:m,80:k,95:T,97:F,98:g}),{5:82,10:5,16:37,17:20,18:38,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,31:e,33:s,35:n,36:21,40:i,41:22,44:u,45:r,47:a,48:c,50:o,52:l,53:h,54:A,55:p,56:f,66:y,67:d,69:E,73:C,74:b,76:m,80:k,95:T,97:F,98:g},t(D,[2,17]),t(D,[2,27]),t(D,[2,28]),{13:[1,84],16:37,17:83,18:38,74:b,80:k,95:T,97:F,98:g},{49:85,57:54,58:55,59:_,60:S,61:N,62:L,63:v,64:$,65:O},t(D,[2,46]),{58:86,64:$,65:O},t(P,[2,62],{57:87,59:_,60:S,61:N,62:L,63:v}),t(M,[2,63]),t(M,[2,64]),t(M,[2,65]),t(M,[2,66]),t(M,[2,67]),t(G,[2,68]),t(G,[2,69]),{8:[1,89],23:90,38:88,41:22,44:u},{16:91,74:b,80:k,95:T,97:F},{43:92,47:U},{46:[1,94]},{13:[1,95]},{13:[1,96]},{70:[1,97],72:[1,98]},{21:z,73:K,74:Y,75:99,77:100,79:101,80:j,81:Q,82:X,83:W,84:q,85:H},{74:[1,111]},{13:x,51:112},t(D,[2,54]),t(D,[2,117]),t(R,[2,13]),t(R,[2,14]),t(R,[2,15]),{37:[2,32]},{15:113,16:79,37:[2,9],74:b,80:k,95:T,97:F},t(J,[2,40],{11:114,12:[1,115]}),t(B,[2,7]),{9:[1,116]},t(V,[2,49]),{16:37,17:117,18:38,74:b,80:k,95:T,97:F,98:g},{13:[1,119],16:37,17:118,18:38,74:b,80:k,95:T,97:F,98:g},t(P,[2,61],{57:120,59:_,60:S,61:N,62:L,63:v}),t(P,[2,60]),{39:[1,121]},{23:90,38:122,41:22,44:u},{8:[1,123],39:[2,33]},t(I,[2,37],{37:[1,124]}),{39:[1,125]},{39:[2,43],43:126,47:U},{16:37,17:127,18:38,74:b,80:k,95:T,97:F,98:g},t(D,[2,70],{13:[1,128]}),t(D,[2,72],{13:[1,130],68:[1,129]}),t(D,[2,76],{13:[1,131],71:[1,132]}),{13:[1,133]},t(D,[2,84],{78:[1,134]}),t(Z,[2,86],{79:135,21:z,73:K,74:Y,80:j,81:Q,82:X,83:W,84:q,85:H}),t(tt,[2,88]),t(tt,[2,90]),t(tt,[2,91]),t(tt,[2,92]),t(tt,[2,93]),t(tt,[2,94]),t(tt,[2,95]),t(tt,[2,96]),t(tt,[2,97]),t(tt,[2,98]),t(D,[2,85]),t(D,[2,53]),{37:[2,10]},t(J,[2,41]),{13:[1,136]},{1:[2,4]},t(V,[2,51]),t(V,[2,50]),{16:37,17:137,18:38,74:b,80:k,95:T,97:F,98:g},t(P,[2,59]),t(D,[2,30]),{39:[1,138]},{23:90,38:139,39:[2,34],41:22,44:u},{43:140,47:U},t(I,[2,38]),{39:[2,44]},t(D,[2,42]),t(D,[2,71]),t(D,[2,73]),t(D,[2,74],{68:[1,141]}),t(D,[2,77]),t(D,[2,78],{13:[1,142]}),t(D,[2,80],{13:[1,144],68:[1,143]}),{21:z,73:K,74:Y,77:145,79:101,80:j,81:Q,82:X,83:W,84:q,85:H},t(tt,[2,89]),{14:[1,146]},t(V,[2,52]),t(D,[2,31]),{39:[2,35]},{39:[1,147]},t(D,[2,75]),t(D,[2,79]),t(D,[2,81]),t(D,[2,82],{68:[1,148]}),t(Z,[2,87],{79:135,21:z,73:K,74:Y,80:j,81:Q,82:X,83:W,84:q,85:H}),t(J,[2,8]),t(I,[2,39]),t(D,[2,83])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],78:[2,32],113:[2,10],116:[2,4],126:[2,44],139:[2,35]},parseError:function t(e,s){if(s.recoverable){this.trace(e)}else{var n=new Error(e);n.hash=s;throw n}},parse:function t(e){var s=this,n=[0],i=[],u=[null],r=[],a=this.table,c="",o=0,l=0,h=2,A=1;var p=r.slice.call(arguments,1);var f=Object.create(this.lexer);var y={yy:{}};for(var d in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,d)){y.yy[d]=this.yy[d]}}f.setInput(e,y.yy);y.yy.lexer=f;y.yy.parser=this;if(typeof f.yylloc=="undefined"){f.yylloc={}}var E=f.yylloc;r.push(E);var C=f.options&&f.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function b(){var t;t=i.pop()||f.lex()||A;if(typeof t!=="number"){if(t instanceof Array){i=t;t=i.pop()}t=s.symbols_[t]||t}return t}var m,k,T,F,g={},B,D,_,S;while(true){k=n[n.length-1];if(this.defaultActions[k]){T=this.defaultActions[k]}else{if(m===null||typeof m=="undefined"){m=b()}T=a[k]&&a[k][m]}if(typeof T==="undefined"||!T.length||!T[0]){var N="";S=[];for(B in a[k]){if(this.terminals_[B]&&B>h){S.push("'"+this.terminals_[B]+"'")}}if(f.showPosition){N="Parse error on line "+(o+1)+":\n"+f.showPosition()+"\nExpecting "+S.join(", ")+", got '"+(this.terminals_[m]||m)+"'"}else{N="Parse error on line "+(o+1)+": Unexpected "+(m==A?"end of input":"'"+(this.terminals_[m]||m)+"'")}this.parseError(N,{text:f.match,token:this.terminals_[m]||m,line:f.yylineno,loc:E,expected:S})}if(T[0]instanceof Array&&T.length>1){throw new Error("Parse Error: multiple actions possible at state: "+k+", token: "+m)}switch(T[0]){case 1:n.push(m);u.push(f.yytext);r.push(f.yylloc);n.push(T[1]);m=null;{l=f.yyleng;c=f.yytext;o=f.yylineno;E=f.yylloc}break;case 2:D=this.productions_[T[1]][1];g.$=u[u.length-D];g._$={first_line:r[r.length-(D||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(D||1)].first_column,last_column:r[r.length-1].last_column};if(C){g._$.range=[r[r.length-(D||1)].range[0],r[r.length-1].range[1]]}F=this.performAction.apply(g,[c,l,o,y.yy,T[1],u,r].concat(p));if(typeof F!=="undefined"){return F}if(D){n=n.slice(0,-1*D*2);u=u.slice(0,-1*D);r=r.slice(0,-1*D)}n.push(this.productions_[T[1]][0]);u.push(g.$);r.push(g._$);_=a[n[n.length-2]][n[n.length-1]];n.push(_);break;case 3:return true}}return true}};var st=function(){var t={EOF:1,parseError:function t(e,s){if(this.yy.parser){this.yy.parser.parseError(e,s)}else{throw new Error(e)}},setInput:function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var e=t.length;var s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var i=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===n.length?this.yylloc.first_column:0)+n[n.length-s.length].length-s[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[i[0],i[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var s,n,i;if(this.options.backtrack_lexer){i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){i.yylloc.range=this.yylloc.range.slice(0)}}n=t[0].match(/(?:\r\n?|\n).*/g);if(n){this.yylineno+=n.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];s=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var u in i){this[u]=i[u]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,s,n;if(!this._more){this.yytext="";this.match=""}var i=this._currentRules();for(var u=0;u<i.length;u++){s=this._input.match(this.rules[i[u]]);if(s&&(!e||s[0].length>e[0].length)){e=s;n=u;if(this.options.backtrack_lexer){t=this.test_match(s,i[u]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,i[n]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var e=this.next();if(e){return e}else{return this.lex()}},begin:function t(e){this.conditionStack.push(e)},popState:function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}},pushState:function t(e){this.begin(e)},stateStackSize:function t(){return this.conditionStack.length},options:{},performAction:function t(e,s,n,i){switch(n){case 0:return 53;case 1:return 54;case 2:return 55;case 3:return 56;case 4:break;case 5:break;case 6:this.begin("acc_title");return 31;case 7:this.popState();return"acc_title_value";case 8:this.begin("acc_descr");return 33;case 9:this.popState();return"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 8;case 14:break;case 15:return 7;case 16:return 7;case 17:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState();this.begin("callback_args");break;case 21:return 70;case 22:this.popState();break;case 23:return 71;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("string");break;case 27:return 73;case 28:this.begin("namespace");return 40;case 29:this.popState();return 8;case 30:break;case 31:this.begin("namespace-body");return 37;case 32:this.popState();return 39;case 33:return"EOF_IN_STRUCT";case 34:return 8;case 35:break;case 36:return"EDGE_STATE";case 37:this.begin("class");return 44;case 38:this.popState();return 8;case 39:break;case 40:this.popState();this.popState();return 39;case 41:this.begin("class-body");return 37;case 42:this.popState();return 39;case 43:return"EOF_IN_STRUCT";case 44:return"EDGE_STATE";case 45:return"OPEN_IN_STRUCT";case 46:break;case 47:return"MEMBER";case 48:return 76;case 49:return 66;case 50:return 67;case 51:return 69;case 52:return 50;case 53:return 52;case 54:return 45;case 55:return 46;case 56:return 72;case 57:this.popState();break;case 58:return"GENERICTYPE";case 59:this.begin("generic");break;case 60:this.popState();break;case 61:return"BQUOTE_STR";case 62:this.begin("bqstring");break;case 63:return 68;case 64:return 68;case 65:return 68;case 66:return 68;case 67:return 60;case 68:return 60;case 69:return 62;case 70:return 62;case 71:return 61;case 72:return 59;case 73:return 63;case 74:return 64;case 75:return 65;case 76:return 21;case 77:return 42;case 78:return 95;case 79:return"DOT";case 80:return"PLUS";case 81:return 81;case 82:return 78;case 83:return 84;case 84:return 84;case 85:return 85;case 86:return"EQUALS";case 87:return"EQUALS";case 88:return 74;case 89:return 12;case 90:return 14;case 91:return"PUNCTUATION";case 92:return 80;case 93:return 97;case 94:return 83;case 95:return 83;case 96:return 9}},rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,32,33,34,35,36,37,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},namespace:{rules:[26,28,29,30,31,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},"class-body":{rules:[26,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},class:{rules:[26,38,39,40,41,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},acc_descr_multiline:{rules:[11,12,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},acc_descr:{rules:[9,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},acc_title:{rules:[7,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},callback_args:{rules:[22,23,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},callback_name:{rules:[19,20,21,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},href:{rules:[26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},struct:{rules:[26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},generic:{rules:[26,48,49,50,51,52,53,54,55,56,57,58,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},bqstring:{rules:[26,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},string:{rules:[24,25,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,37,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96],inclusive:true}}};return t}();et.lexer=st;function nt(){this.yy={}}nt.prototype=et;et.Parser=nt;return new nt}();u.parser=u;const r=u;const a=["#","+","~","-",""];class c{constructor(t,e){this.memberType=e;this.visibility="";this.classifier="";const s=(0,i.d)(t,(0,i.c)());this.parseMember(s)}getDisplayDetails(){let t=this.visibility+(0,i.v)(this.id);if(this.memberType==="method"){t+=`(${(0,i.v)(this.parameters.trim())})`;if(this.returnType){t+=" : "+(0,i.v)(this.returnType)}}t=t.trim();const e=this.parseClassifier();return{displayText:t,cssStyle:e}}parseMember(t){let e="";if(this.memberType==="method"){const s=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/;const n=t.match(s);if(n){const t=n[1]?n[1].trim():"";if(a.includes(t)){this.visibility=t}this.id=n[2].trim();this.parameters=n[3]?n[3].trim():"";e=n[4]?n[4].trim():"";this.returnType=n[5]?n[5].trim():"";if(e===""){const t=this.returnType.substring(this.returnType.length-1);if(t.match(/[$*]/)){e=t;this.returnType=this.returnType.substring(0,this.returnType.length-1)}}}}else{const s=t.length;const n=t.substring(0,1);const i=t.substring(s-1);if(a.includes(n)){this.visibility=n}if(i.match(/[$*]/)){e=i}this.id=t.substring(this.visibility===""?0:1,e===""?s:s-1)}this.classifier=e}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}}const o="classId-";let l=[];let h={};let A=[];let p=0;let f={};let y=0;let d=[];const E=t=>i.e.sanitizeText(t,(0,i.c)());const C=function(t){const e=i.e.sanitizeText(t,(0,i.c)());let s="";let n=e;if(e.indexOf("~")>0){const t=e.split("~");n=E(t[0]);s=E(t[1])}return{className:n,type:s}};const b=function(t,e){const s=i.e.sanitizeText(t,(0,i.c)());if(e){e=E(e)}const{className:n}=C(s);h[n].label=e};const m=function(t){const e=i.e.sanitizeText(t,(0,i.c)());const{className:s,type:n}=C(e);if(Object.hasOwn(h,s)){return}const u=i.e.sanitizeText(s,(0,i.c)());h[u]={id:u,type:n,label:u,cssClasses:[],methods:[],members:[],annotations:[],styles:[],domId:o+u+"-"+p};p++};const k=function(t){const e=i.e.sanitizeText(t,(0,i.c)());if(e in h){return h[e].domId}throw new Error("Class not found: "+e)};const T=function(){l=[];h={};A=[];d=[];d.push(z);f={};y=0;(0,i.t)()};const F=function(t){return h[t]};const g=function(){return h};const B=function(){return l};const D=function(){return A};const _=function(t){i.l.debug("Adding relation: "+JSON.stringify(t));m(t.id1);m(t.id2);t.id1=C(t.id1).className;t.id2=C(t.id2).className;t.relationTitle1=i.e.sanitizeText(t.relationTitle1.trim(),(0,i.c)());t.relationTitle2=i.e.sanitizeText(t.relationTitle2.trim(),(0,i.c)());l.push(t)};const S=function(t,e){const s=C(t).className;h[s].annotations.push(e)};const N=function(t,e){m(t);const s=C(t).className;const n=h[s];if(typeof e==="string"){const t=e.trim();if(t.startsWith("<<")&&t.endsWith(">>")){n.annotations.push(E(t.substring(2,t.length-2)))}else if(t.indexOf(")")>0){n.methods.push(new c(t,"method"))}else if(t){n.members.push(new c(t,"attribute"))}}};const L=function(t,e){if(Array.isArray(e)){e.reverse();e.forEach((e=>N(t,e)))}};const v=function(t,e){const s={id:`note${A.length}`,class:e,text:t};A.push(s)};const $=function(t){if(t.startsWith(":")){t=t.substring(1)}return E(t.trim())};const O=function(t,e){t.split(",").forEach((function(t){let s=t;if(t[0].match(/\d/)){s=o+s}if(h[s]!==void 0){h[s].cssClasses.push(e)}}))};const I=function(t,e){t.split(",").forEach((function(t){if(e!==void 0){h[t].tooltip=E(e)}}))};const x=function(t,e){if(e){return f[e].classes[t].tooltip}return h[t].tooltip};const R=function(t,e,s){const n=(0,i.c)();t.split(",").forEach((function(t){let u=t;if(t[0].match(/\d/)){u=o+u}if(h[u]!==void 0){h[u].link=i.u.formatUrl(e,n);if(n.securityLevel==="sandbox"){h[u].linkTarget="_top"}else if(typeof s==="string"){h[u].linkTarget=E(s)}else{h[u].linkTarget="_blank"}}}));O(t,"clickable")};const w=function(t,e,s){t.split(",").forEach((function(t){P(t,e,s);h[t].haveCallback=true}));O(t,"clickable")};const P=function(t,e,s){const n=i.e.sanitizeText(t,(0,i.c)());const u=(0,i.c)();if(u.securityLevel!=="loose"){return}if(e===void 0){return}const r=n;if(h[r]!==void 0){const t=k(r);let n=[];if(typeof s==="string"){n=s.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let t=0;t<n.length;t++){let e=n[t].trim();if(e.charAt(0)==='"'&&e.charAt(e.length-1)==='"'){e=e.substr(1,e.length-2)}n[t]=e}}if(n.length===0){n.push(t)}d.push((function(){const s=document.querySelector(`[id="${t}"]`);if(s!==null){s.addEventListener("click",(function(){i.u.runFunc(e,...n)}),false)}}))}};const M=function(t){d.forEach((function(e){e(t)}))};const G={LINE:0,DOTTED_LINE:1};const U={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4};const z=function(t){let e=(0,n.Ltv)(".mermaidTooltip");if((e._groups||e)[0][0]===null){e=(0,n.Ltv)("body").append("div").attr("class","mermaidTooltip").style("opacity",0)}const s=(0,n.Ltv)(t).select("svg");const i=s.selectAll("g.node");i.on("mouseover",(function(){const t=(0,n.Ltv)(this);const s=t.attr("title");if(s===null){return}const i=this.getBoundingClientRect();e.transition().duration(200).style("opacity",".9");e.text(t.attr("title")).style("left",window.scrollX+i.left+(i.right-i.left)/2+"px").style("top",window.scrollY+i.top-14+document.body.scrollTop+"px");e.html(e.html().replace(/&lt;br\/&gt;/g,"<br/>"));t.classed("hover",true)})).on("mouseout",(function(){e.transition().duration(500).style("opacity",0);const t=(0,n.Ltv)(this);t.classed("hover",false)}))};d.push(z);let K="TB";const Y=()=>K;const j=t=>{K=t};const Q=function(t){if(f[t]!==void 0){return}f[t]={id:t,classes:{},children:{},domId:o+t+"-"+y};y++};const X=function(t){return f[t]};const W=function(){return f};const q=function(t,e){if(f[t]===void 0){return}for(const s of e){const{className:e}=C(s);h[e].parent=t;f[t].classes[e]=h[e]}};const H=function(t,e){const s=h[t];if(!e||!s){return}for(const n of e){if(n.includes(",")){s.styles.push(...n.split(","))}else{s.styles.push(n)}}};const J={setAccTitle:i.s,getAccTitle:i.g,getAccDescription:i.a,setAccDescription:i.b,getConfig:()=>(0,i.c)().class,addClass:m,bindFunctions:M,clear:T,getClass:F,getClasses:g,getNotes:D,addAnnotation:S,addNote:v,getRelations:B,addRelation:_,getDirection:Y,setDirection:j,addMember:N,addMembers:L,cleanupLabel:$,lineType:G,relationType:U,setClickEvent:w,setCssClass:O,setLink:R,getTooltip:x,setTooltip:I,lookUpDomId:k,setDiagramTitle:i.q,getDiagramTitle:i.r,setClassLabel:b,addNamespace:Q,addClassesToNamespace:q,getNamespace:X,getNamespaces:W,setCssStyle:H};const V=t=>`g.classGroup text {\n  fill: ${t.nodeBorder||t.classText};\n  stroke: none;\n  font-family: ${t.fontFamily};\n  font-size: 10px;\n\n  .title {\n    font-weight: bolder;\n  }\n\n}\n\n.nodeLabel, .edgeLabel {\n  color: ${t.classText};\n}\n.edgeLabel .label rect {\n  fill: ${t.mainBkg};\n}\n.label text {\n  fill: ${t.classText};\n}\n.edgeLabel .label span {\n  background: ${t.mainBkg};\n}\n\n.classTitle {\n  font-weight: bolder;\n}\n.node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${t.mainBkg};\n    stroke: ${t.nodeBorder};\n    stroke-width: 1px;\n  }\n\n\n.divider {\n  stroke: ${t.nodeBorder};\n  stroke-width: 1;\n}\n\ng.clickable {\n  cursor: pointer;\n}\n\ng.classGroup rect {\n  fill: ${t.mainBkg};\n  stroke: ${t.nodeBorder};\n}\n\ng.classGroup line {\n  stroke: ${t.nodeBorder};\n  stroke-width: 1;\n}\n\n.classLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${t.mainBkg};\n  opacity: 0.5;\n}\n\n.classLabel .label {\n  fill: ${t.nodeBorder};\n  font-size: 10px;\n}\n\n.relation {\n  stroke: ${t.lineColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.dashed-line{\n  stroke-dasharray: 3;\n}\n\n.dotted-line{\n  stroke-dasharray: 1 2;\n}\n\n#compositionStart, .composition {\n  fill: ${t.lineColor} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#compositionEnd, .composition {\n  fill: ${t.lineColor} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${t.lineColor} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${t.lineColor} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionStart, .extension {\n  fill: transparent !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionEnd, .extension {\n  fill: transparent !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationStart, .aggregation {\n  fill: transparent !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationEnd, .aggregation {\n  fill: transparent !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopStart, .lollipop {\n  fill: ${t.mainBkg} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopEnd, .lollipop {\n  fill: ${t.mainBkg} !important;\n  stroke: ${t.lineColor} !important;\n  stroke-width: 1;\n}\n\n.edgeTerminals {\n  font-size: 11px;\n  line-height: initial;\n}\n\n.classTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${t.textColor};\n}\n`;const Z=V}}]);