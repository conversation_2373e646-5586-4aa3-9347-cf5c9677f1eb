"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[6483],{86483:(t,e,r)=>{r.d(e,{diagram:()=>mt});var i=r(76235);var n=r(1056);var s=r(74353);var a=r.n(s);var c=r(16750);var o=r(42838);var l=r.n(o);var h=function(){var t=function(t,e,r,i){for(r=r||{},i=t.length;i--;r[t[i]]=e);return r},e=[1,3],r=[1,6],i=[1,4],n=[1,5],s=[2,5],a=[1,12],c=[5,7,13,19,21,23,24,26,28,31,37,40,47],o=[7,13,19,21,23,24,26,28,31,37,40],l=[7,12,13,19,21,23,24,26,28,31,37,40],h=[7,13,47],m=[1,42],u=[1,41],y=[7,13,29,32,35,38,47],f=[1,55],p=[1,56],b=[1,57],g=[7,13,32,35,42,47];var d={trace:function t(){},yy:{},symbols_:{error:2,start:3,eol:4,GG:5,document:6,EOF:7,":":8,DIR:9,options:10,body:11,OPT:12,NL:13,line:14,statement:15,commitStatement:16,mergeStatement:17,cherryPickStatement:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,section:24,branchStatement:25,CHECKOUT:26,ref:27,BRANCH:28,ORDER:29,NUM:30,CHERRY_PICK:31,COMMIT_ID:32,STR:33,PARENT_COMMIT:34,COMMIT_TAG:35,EMPTYSTR:36,MERGE:37,COMMIT_TYPE:38,commitType:39,COMMIT:40,commit_arg:41,COMMIT_MSG:42,NORMAL:43,REVERSE:44,HIGHLIGHT:45,ID:46,";":47,$accept:0,$end:1},terminals_:{2:"error",5:"GG",7:"EOF",8:":",9:"DIR",12:"OPT",13:"NL",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"section",26:"CHECKOUT",28:"BRANCH",29:"ORDER",30:"NUM",31:"CHERRY_PICK",32:"COMMIT_ID",33:"STR",34:"PARENT_COMMIT",35:"COMMIT_TAG",36:"EMPTYSTR",37:"MERGE",38:"COMMIT_TYPE",40:"COMMIT",42:"COMMIT_MSG",43:"NORMAL",44:"REVERSE",45:"HIGHLIGHT",46:"ID",47:";"},productions_:[0,[3,2],[3,3],[3,4],[3,5],[6,0],[6,2],[10,2],[10,1],[11,0],[11,2],[14,2],[14,1],[15,1],[15,1],[15,1],[15,2],[15,2],[15,1],[15,1],[15,1],[15,2],[25,2],[25,4],[18,3],[18,5],[18,5],[18,7],[18,7],[18,5],[18,5],[18,5],[18,7],[18,7],[18,7],[18,7],[17,2],[17,4],[17,4],[17,4],[17,6],[17,6],[17,6],[17,6],[17,6],[17,6],[17,8],[17,8],[17,8],[17,8],[17,8],[17,8],[16,2],[16,3],[16,3],[16,5],[16,5],[16,3],[16,5],[16,5],[16,5],[16,5],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,3],[16,5],[16,5],[16,5],[16,5],[16,5],[16,5],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,7],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[16,9],[41,0],[41,1],[39,1],[39,1],[39,1],[27,1],[27,1],[4,1],[4,1],[4,1]],performAction:function t(e,r,i,n,s,a,c){var o=a.length-1;switch(s){case 2:return a[o];case 3:return a[o-1];case 4:n.setDirection(a[o-3]);return a[o-1];case 6:n.setOptions(a[o-1]);this.$=a[o];break;case 7:a[o-1]+=a[o];this.$=a[o-1];break;case 9:this.$=[];break;case 10:a[o-1].push(a[o]);this.$=a[o-1];break;case 11:this.$=a[o-1];break;case 16:this.$=a[o].trim();n.setAccTitle(this.$);break;case 17:case 18:this.$=a[o].trim();n.setAccDescription(this.$);break;case 19:n.addSection(a[o].substr(8));this.$=a[o].substr(8);break;case 21:n.checkout(a[o]);break;case 22:n.branch(a[o]);break;case 23:n.branch(a[o-2],a[o]);break;case 24:n.cherryPick(a[o],"",void 0);break;case 25:n.cherryPick(a[o-2],"",void 0,a[o]);break;case 26:n.cherryPick(a[o-2],"",a[o]);break;case 27:n.cherryPick(a[o-4],"",a[o],a[o-2]);break;case 28:n.cherryPick(a[o-4],"",a[o-2],a[o]);break;case 29:n.cherryPick(a[o],"",a[o-2]);break;case 30:n.cherryPick(a[o],"","");break;case 31:n.cherryPick(a[o-2],"","");break;case 32:n.cherryPick(a[o-4],"","",a[o-2]);break;case 33:n.cherryPick(a[o-4],"","",a[o]);break;case 34:n.cherryPick(a[o-2],"",a[o-4],a[o]);break;case 35:n.cherryPick(a[o-2],"","",a[o]);break;case 36:n.merge(a[o],"","","");break;case 37:n.merge(a[o-2],a[o],"","");break;case 38:n.merge(a[o-2],"",a[o],"");break;case 39:n.merge(a[o-2],"","",a[o]);break;case 40:n.merge(a[o-4],a[o],"",a[o-2]);break;case 41:n.merge(a[o-4],"",a[o],a[o-2]);break;case 42:n.merge(a[o-4],"",a[o-2],a[o]);break;case 43:n.merge(a[o-4],a[o-2],a[o],"");break;case 44:n.merge(a[o-4],a[o-2],"",a[o]);break;case 45:n.merge(a[o-4],a[o],a[o-2],"");break;case 46:n.merge(a[o-6],a[o-4],a[o-2],a[o]);break;case 47:n.merge(a[o-6],a[o],a[o-4],a[o-2]);break;case 48:n.merge(a[o-6],a[o-4],a[o],a[o-2]);break;case 49:n.merge(a[o-6],a[o-2],a[o-4],a[o]);break;case 50:n.merge(a[o-6],a[o],a[o-2],a[o-4]);break;case 51:n.merge(a[o-6],a[o-2],a[o],a[o-4]);break;case 52:n.commit(a[o]);break;case 53:n.commit("","",n.commitType.NORMAL,a[o]);break;case 54:n.commit("","",a[o],"");break;case 55:n.commit("","",a[o],a[o-2]);break;case 56:n.commit("","",a[o-2],a[o]);break;case 57:n.commit("",a[o],n.commitType.NORMAL,"");break;case 58:n.commit("",a[o-2],n.commitType.NORMAL,a[o]);break;case 59:n.commit("",a[o],n.commitType.NORMAL,a[o-2]);break;case 60:n.commit("",a[o-2],a[o],"");break;case 61:n.commit("",a[o],a[o-2],"");break;case 62:n.commit("",a[o-4],a[o-2],a[o]);break;case 63:n.commit("",a[o-4],a[o],a[o-2]);break;case 64:n.commit("",a[o-2],a[o-4],a[o]);break;case 65:n.commit("",a[o],a[o-4],a[o-2]);break;case 66:n.commit("",a[o],a[o-2],a[o-4]);break;case 67:n.commit("",a[o-2],a[o],a[o-4]);break;case 68:n.commit(a[o],"",n.commitType.NORMAL,"");break;case 69:n.commit(a[o],"",n.commitType.NORMAL,a[o-2]);break;case 70:n.commit(a[o-2],"",n.commitType.NORMAL,a[o]);break;case 71:n.commit(a[o-2],"",a[o],"");break;case 72:n.commit(a[o],"",a[o-2],"");break;case 73:n.commit(a[o],a[o-2],n.commitType.NORMAL,"");break;case 74:n.commit(a[o-2],a[o],n.commitType.NORMAL,"");break;case 75:n.commit(a[o-4],"",a[o-2],a[o]);break;case 76:n.commit(a[o-4],"",a[o],a[o-2]);break;case 77:n.commit(a[o-2],"",a[o-4],a[o]);break;case 78:n.commit(a[o],"",a[o-4],a[o-2]);break;case 79:n.commit(a[o],"",a[o-2],a[o-4]);break;case 80:n.commit(a[o-2],"",a[o],a[o-4]);break;case 81:n.commit(a[o-4],a[o],a[o-2],"");break;case 82:n.commit(a[o-4],a[o-2],a[o],"");break;case 83:n.commit(a[o-2],a[o],a[o-4],"");break;case 84:n.commit(a[o],a[o-2],a[o-4],"");break;case 85:n.commit(a[o],a[o-4],a[o-2],"");break;case 86:n.commit(a[o-2],a[o-4],a[o],"");break;case 87:n.commit(a[o-4],a[o],n.commitType.NORMAL,a[o-2]);break;case 88:n.commit(a[o-4],a[o-2],n.commitType.NORMAL,a[o]);break;case 89:n.commit(a[o-2],a[o],n.commitType.NORMAL,a[o-4]);break;case 90:n.commit(a[o],a[o-2],n.commitType.NORMAL,a[o-4]);break;case 91:n.commit(a[o],a[o-4],n.commitType.NORMAL,a[o-2]);break;case 92:n.commit(a[o-2],a[o-4],n.commitType.NORMAL,a[o]);break;case 93:n.commit(a[o-6],a[o-4],a[o-2],a[o]);break;case 94:n.commit(a[o-6],a[o-4],a[o],a[o-2]);break;case 95:n.commit(a[o-6],a[o-2],a[o-4],a[o]);break;case 96:n.commit(a[o-6],a[o],a[o-4],a[o-2]);break;case 97:n.commit(a[o-6],a[o-2],a[o],a[o-4]);break;case 98:n.commit(a[o-6],a[o],a[o-2],a[o-4]);break;case 99:n.commit(a[o-4],a[o-6],a[o-2],a[o]);break;case 100:n.commit(a[o-4],a[o-6],a[o],a[o-2]);break;case 101:n.commit(a[o-2],a[o-6],a[o-4],a[o]);break;case 102:n.commit(a[o],a[o-6],a[o-4],a[o-2]);break;case 103:n.commit(a[o-2],a[o-6],a[o],a[o-4]);break;case 104:n.commit(a[o],a[o-6],a[o-2],a[o-4]);break;case 105:n.commit(a[o],a[o-4],a[o-2],a[o-6]);break;case 106:n.commit(a[o-2],a[o-4],a[o],a[o-6]);break;case 107:n.commit(a[o],a[o-2],a[o-4],a[o-6]);break;case 108:n.commit(a[o-2],a[o],a[o-4],a[o-6]);break;case 109:n.commit(a[o-4],a[o-2],a[o],a[o-6]);break;case 110:n.commit(a[o-4],a[o],a[o-2],a[o-6]);break;case 111:n.commit(a[o-2],a[o-4],a[o-6],a[o]);break;case 112:n.commit(a[o],a[o-4],a[o-6],a[o-2]);break;case 113:n.commit(a[o-2],a[o],a[o-6],a[o-4]);break;case 114:n.commit(a[o],a[o-2],a[o-6],a[o-4]);break;case 115:n.commit(a[o-4],a[o-2],a[o-6],a[o]);break;case 116:n.commit(a[o-4],a[o],a[o-6],a[o-2]);break;case 117:this.$="";break;case 118:this.$=a[o];break;case 119:this.$=n.commitType.NORMAL;break;case 120:this.$=n.commitType.REVERSE;break;case 121:this.$=n.commitType.HIGHLIGHT;break}},table:[{3:1,4:2,5:e,7:r,13:i,47:n},{1:[3]},{3:7,4:2,5:e,7:r,13:i,47:n},{6:8,7:s,8:[1,9],9:[1,10],10:11,13:a},t(c,[2,124]),t(c,[2,125]),t(c,[2,126]),{1:[2,1]},{7:[1,13]},{6:14,7:s,10:11,13:a},{8:[1,15]},t(o,[2,9],{11:16,12:[1,17]}),t(l,[2,8]),{1:[2,2]},{7:[1,18]},{6:19,7:s,10:11,13:a},{7:[2,6],13:[1,22],14:20,15:21,16:23,17:24,18:25,19:[1,26],21:[1,27],23:[1,28],24:[1,29],25:30,26:[1,31],28:[1,35],31:[1,34],37:[1,33],40:[1,32]},t(l,[2,7]),{1:[2,3]},{7:[1,36]},t(o,[2,10]),{4:37,7:r,13:i,47:n},t(o,[2,12]),t(h,[2,13]),t(h,[2,14]),t(h,[2,15]),{20:[1,38]},{22:[1,39]},t(h,[2,18]),t(h,[2,19]),t(h,[2,20]),{27:40,33:m,46:u},t(h,[2,117],{41:43,32:[1,46],33:[1,48],35:[1,44],38:[1,45],42:[1,47]}),{27:49,33:m,46:u},{32:[1,50],35:[1,51]},{27:52,33:m,46:u},{1:[2,4]},t(o,[2,11]),t(h,[2,16]),t(h,[2,17]),t(h,[2,21]),t(y,[2,122]),t(y,[2,123]),t(h,[2,52]),{33:[1,53]},{39:54,43:f,44:p,45:b},{33:[1,58]},{33:[1,59]},t(h,[2,118]),t(h,[2,36],{32:[1,60],35:[1,62],38:[1,61]}),{33:[1,63]},{33:[1,64],36:[1,65]},t(h,[2,22],{29:[1,66]}),t(h,[2,53],{32:[1,68],38:[1,67],42:[1,69]}),t(h,[2,54],{32:[1,71],35:[1,70],42:[1,72]}),t(g,[2,119]),t(g,[2,120]),t(g,[2,121]),t(h,[2,57],{35:[1,73],38:[1,74],42:[1,75]}),t(h,[2,68],{32:[1,78],35:[1,76],38:[1,77]}),{33:[1,79]},{39:80,43:f,44:p,45:b},{33:[1,81]},t(h,[2,24],{34:[1,82],35:[1,83]}),{32:[1,84]},{32:[1,85]},{30:[1,86]},{39:87,43:f,44:p,45:b},{33:[1,88]},{33:[1,89]},{33:[1,90]},{33:[1,91]},{33:[1,92]},{33:[1,93]},{39:94,43:f,44:p,45:b},{33:[1,95]},{33:[1,96]},{39:97,43:f,44:p,45:b},{33:[1,98]},t(h,[2,37],{35:[1,100],38:[1,99]}),t(h,[2,38],{32:[1,102],35:[1,101]}),t(h,[2,39],{32:[1,103],38:[1,104]}),{33:[1,105]},{33:[1,106],36:[1,107]},{33:[1,108]},{33:[1,109]},t(h,[2,23]),t(h,[2,55],{32:[1,110],42:[1,111]}),t(h,[2,59],{38:[1,112],42:[1,113]}),t(h,[2,69],{32:[1,115],38:[1,114]}),t(h,[2,56],{32:[1,116],42:[1,117]}),t(h,[2,61],{35:[1,118],42:[1,119]}),t(h,[2,72],{32:[1,121],35:[1,120]}),t(h,[2,58],{38:[1,122],42:[1,123]}),t(h,[2,60],{35:[1,124],42:[1,125]}),t(h,[2,73],{35:[1,127],38:[1,126]}),t(h,[2,70],{32:[1,129],38:[1,128]}),t(h,[2,71],{32:[1,131],35:[1,130]}),t(h,[2,74],{35:[1,133],38:[1,132]}),{39:134,43:f,44:p,45:b},{33:[1,135]},{33:[1,136]},{33:[1,137]},{33:[1,138]},{39:139,43:f,44:p,45:b},t(h,[2,25],{35:[1,140]}),t(h,[2,26],{34:[1,141]}),t(h,[2,31],{34:[1,142]}),t(h,[2,29],{34:[1,143]}),t(h,[2,30],{34:[1,144]}),{33:[1,145]},{33:[1,146]},{39:147,43:f,44:p,45:b},{33:[1,148]},{39:149,43:f,44:p,45:b},{33:[1,150]},{33:[1,151]},{33:[1,152]},{33:[1,153]},{33:[1,154]},{33:[1,155]},{33:[1,156]},{39:157,43:f,44:p,45:b},{33:[1,158]},{33:[1,159]},{33:[1,160]},{39:161,43:f,44:p,45:b},{33:[1,162]},{39:163,43:f,44:p,45:b},{33:[1,164]},{33:[1,165]},{33:[1,166]},{39:167,43:f,44:p,45:b},{33:[1,168]},t(h,[2,43],{35:[1,169]}),t(h,[2,44],{38:[1,170]}),t(h,[2,42],{32:[1,171]}),t(h,[2,45],{35:[1,172]}),t(h,[2,40],{38:[1,173]}),t(h,[2,41],{32:[1,174]}),{33:[1,175],36:[1,176]},{33:[1,177]},{33:[1,178]},{33:[1,179]},{33:[1,180]},t(h,[2,66],{42:[1,181]}),t(h,[2,79],{32:[1,182]}),t(h,[2,67],{42:[1,183]}),t(h,[2,90],{38:[1,184]}),t(h,[2,80],{32:[1,185]}),t(h,[2,89],{38:[1,186]}),t(h,[2,65],{42:[1,187]}),t(h,[2,78],{32:[1,188]}),t(h,[2,64],{42:[1,189]}),t(h,[2,84],{35:[1,190]}),t(h,[2,77],{32:[1,191]}),t(h,[2,83],{35:[1,192]}),t(h,[2,63],{42:[1,193]}),t(h,[2,91],{38:[1,194]}),t(h,[2,62],{42:[1,195]}),t(h,[2,85],{35:[1,196]}),t(h,[2,86],{35:[1,197]}),t(h,[2,92],{38:[1,198]}),t(h,[2,76],{32:[1,199]}),t(h,[2,87],{38:[1,200]}),t(h,[2,75],{32:[1,201]}),t(h,[2,81],{35:[1,202]}),t(h,[2,82],{35:[1,203]}),t(h,[2,88],{38:[1,204]}),{33:[1,205]},{39:206,43:f,44:p,45:b},{33:[1,207]},{33:[1,208]},{39:209,43:f,44:p,45:b},{33:[1,210]},t(h,[2,27]),t(h,[2,32]),t(h,[2,28]),t(h,[2,33]),t(h,[2,34]),t(h,[2,35]),{33:[1,211]},{33:[1,212]},{33:[1,213]},{39:214,43:f,44:p,45:b},{33:[1,215]},{39:216,43:f,44:p,45:b},{33:[1,217]},{33:[1,218]},{33:[1,219]},{33:[1,220]},{33:[1,221]},{33:[1,222]},{33:[1,223]},{39:224,43:f,44:p,45:b},{33:[1,225]},{33:[1,226]},{33:[1,227]},{39:228,43:f,44:p,45:b},{33:[1,229]},{39:230,43:f,44:p,45:b},{33:[1,231]},{33:[1,232]},{33:[1,233]},{39:234,43:f,44:p,45:b},t(h,[2,46]),t(h,[2,48]),t(h,[2,47]),t(h,[2,49]),t(h,[2,51]),t(h,[2,50]),t(h,[2,107]),t(h,[2,108]),t(h,[2,105]),t(h,[2,106]),t(h,[2,110]),t(h,[2,109]),t(h,[2,114]),t(h,[2,113]),t(h,[2,112]),t(h,[2,111]),t(h,[2,116]),t(h,[2,115]),t(h,[2,104]),t(h,[2,103]),t(h,[2,102]),t(h,[2,101]),t(h,[2,99]),t(h,[2,100]),t(h,[2,98]),t(h,[2,97]),t(h,[2,96]),t(h,[2,95]),t(h,[2,93]),t(h,[2,94])],defaultActions:{7:[2,1],13:[2,2],18:[2,3],36:[2,4]},parseError:function t(e,r){if(r.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=r;throw i}},parse:function t(e){var r=this,i=[0],n=[],s=[null],a=[],c=this.table,o="",l=0,h=0,m=2,u=1;var y=a.slice.call(arguments,1);var f=Object.create(this.lexer);var p={yy:{}};for(var b in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,b)){p.yy[b]=this.yy[b]}}f.setInput(e,p.yy);p.yy.lexer=f;p.yy.parser=this;if(typeof f.yylloc=="undefined"){f.yylloc={}}var g=f.yylloc;a.push(g);var d=f.options&&f.options.ranges;if(typeof p.yy.parseError==="function"){this.parseError=p.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function k(){var t;t=n.pop()||f.lex()||u;if(typeof t!=="number"){if(t instanceof Array){n=t;t=n.pop()}t=r.symbols_[t]||t}return t}var $,x,_,E,w={},T,v,L,R;while(true){x=i[i.length-1];if(this.defaultActions[x]){_=this.defaultActions[x]}else{if($===null||typeof $=="undefined"){$=k()}_=c[x]&&c[x][$]}if(typeof _==="undefined"||!_.length||!_[0]){var M="";R=[];for(T in c[x]){if(this.terminals_[T]&&T>m){R.push("'"+this.terminals_[T]+"'")}}if(f.showPosition){M="Parse error on line "+(l+1)+":\n"+f.showPosition()+"\nExpecting "+R.join(", ")+", got '"+(this.terminals_[$]||$)+"'"}else{M="Parse error on line "+(l+1)+": Unexpected "+($==u?"end of input":"'"+(this.terminals_[$]||$)+"'")}this.parseError(M,{text:f.match,token:this.terminals_[$]||$,line:f.yylineno,loc:g,expected:R})}if(_[0]instanceof Array&&_.length>1){throw new Error("Parse Error: multiple actions possible at state: "+x+", token: "+$)}switch(_[0]){case 1:i.push($);s.push(f.yytext);a.push(f.yylloc);i.push(_[1]);$=null;{h=f.yyleng;o=f.yytext;l=f.yylineno;g=f.yylloc}break;case 2:v=this.productions_[_[1]][1];w.$=s[s.length-v];w._$={first_line:a[a.length-(v||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(v||1)].first_column,last_column:a[a.length-1].last_column};if(d){w._$.range=[a[a.length-(v||1)].range[0],a[a.length-1].range[1]]}E=this.performAction.apply(w,[o,h,l,p.yy,_[1],s,a].concat(y));if(typeof E!=="undefined"){return E}if(v){i=i.slice(0,-1*v*2);s=s.slice(0,-1*v);a=a.slice(0,-1*v)}i.push(this.productions_[_[1]][0]);s.push(w.$);a.push(w._$);L=c[i[i.length-2]][i[i.length-1]];i.push(L);break;case 3:return true}}return true}};var k=function(){var t={EOF:1,parseError:function t(e,r){if(this.yy.parser){this.yy.parser.parseError(e,r)}else{throw new Error(e)}},setInput:function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var e=t.length;var r=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(r.length-1){this.yylineno-=r.length-1}var n=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===i.length?this.yylloc.first_column:0)+i[i.length-r.length].length-r[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[n[0],n[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var r,i,n;if(this.options.backtrack_lexer){n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){n.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];r=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(r){return r}else if(this._backtrack){for(var s in n){this[s]=n[s]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,r,i;if(!this._more){this.yytext="";this.match=""}var n=this._currentRules();for(var s=0;s<n.length;s++){r=this._input.match(this.rules[n[s]]);if(r&&(!e||r[0].length>e[0].length)){e=r;i=s;if(this.options.backtrack_lexer){t=this.test_match(r,n[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,n[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var e=this.next();if(e){return e}else{return this.lex()}},begin:function t(e){this.conditionStack.push(e)},popState:function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}},pushState:function t(e){this.begin(e)},stateStackSize:function t(){return this.conditionStack.length},options:{"case-insensitive":true},performAction:function t(e,r,i,n){switch(i){case 0:this.begin("acc_title");return 19;case 1:this.popState();return"acc_title_value";case 2:this.begin("acc_descr");return 21;case 3:this.popState();return"acc_descr_value";case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 13;case 8:break;case 9:break;case 10:return 5;case 11:return 40;case 12:return 32;case 13:return 38;case 14:return 42;case 15:return 43;case 16:return 44;case 17:return 45;case 18:return 35;case 19:return 28;case 20:return 29;case 21:return 37;case 22:return 31;case 23:return 34;case 24:return 26;case 25:return 9;case 26:return 9;case 27:return 8;case 28:return"CARET";case 29:this.begin("options");break;case 30:this.popState();break;case 31:return 12;case 32:return 36;case 33:this.begin("string");break;case 34:this.popState();break;case 35:return 33;case 36:return 30;case 37:return 46;case 38:return 7}},rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:(\r?\n)+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:gitGraph\b)/i,/^(?:commit(?=\s|$))/i,/^(?:id:)/i,/^(?:type:)/i,/^(?:msg:)/i,/^(?:NORMAL\b)/i,/^(?:REVERSE\b)/i,/^(?:HIGHLIGHT\b)/i,/^(?:tag:)/i,/^(?:branch(?=\s|$))/i,/^(?:order:)/i,/^(?:merge(?=\s|$))/i,/^(?:cherry-pick(?=\s|$))/i,/^(?:parent:)/i,/^(?:checkout(?=\s|$))/i,/^(?:LR\b)/i,/^(?:TB\b)/i,/^(?::)/i,/^(?:\^)/i,/^(?:options\r?\n)/i,/^(?:[ \r\n\t]+end\b)/i,/^(?:[\s\S]+(?=[ \r\n\t]+end))/i,/^(?:["]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[0-9]+(?=\s|$))/i,/^(?:\w([-\./\w]*[-\w])?)/i,/^(?:$)/i,/^(?:\s+)/i],conditions:{acc_descr_multiline:{rules:[5,6],inclusive:false},acc_descr:{rules:[3],inclusive:false},acc_title:{rules:[1],inclusive:false},options:{rules:[30,31],inclusive:false},string:{rules:[34,35],inclusive:false},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32,33,36,37,38,39],inclusive:true}}};return t}();d.lexer=k;function $(){this.yy={}}$.prototype=d;d.Parser=$;return new $}();h.parser=h;const m=h;let u=(0,i.c)().gitGraph.mainBranchName;let y=(0,i.c)().gitGraph.mainBranchOrder;let f={};let p=null;let b={};b[u]={name:u,order:y};let g={};g[u]=p;let d=u;let k="LR";let $=0;function x(){return(0,i.x)({length:7})}function _(t,e){const r=Object.create(null);return t.reduce(((t,i)=>{const n=e(i);if(!r[n]){r[n]=true;t.push(i)}return t}),[])}const E=function(t){k=t};let w={};const T=function(t){i.l.debug("options str",t);t=t&&t.trim();t=t||"{}";try{w=JSON.parse(t)}catch(e){i.l.error("error while parsing gitGraph options",e.message)}};const v=function(){return w};const L=function(t,e,r,n){i.l.debug("Entering commit:",t,e,r,n);e=i.e.sanitizeText(e,(0,i.c)());t=i.e.sanitizeText(t,(0,i.c)());n=i.e.sanitizeText(n,(0,i.c)());const s={id:e?e:$+"-"+x(),message:t,seq:$++,type:r?r:q.NORMAL,tag:n?n:"",parents:p==null?[]:[p.id],branch:d};p=s;f[s.id]=s;g[d]=s.id;i.l.debug("in pushCommit "+s.id)};const R=function(t,e){t=i.e.sanitizeText(t,(0,i.c)());if(g[t]===void 0){g[t]=p!=null?p.id:null;b[t]={name:t,order:e?parseInt(e,10):null};A(t);i.l.debug("in createBranch")}else{let e=new Error('Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout '+t+'")');e.hash={text:"branch "+t,token:"branch "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:['"checkout '+t+'"']};throw e}};const M=function(t,e,r,n){t=i.e.sanitizeText(t,(0,i.c)());e=i.e.sanitizeText(e,(0,i.c)());const s=f[g[d]];const a=f[g[t]];if(d===t){let e=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');e.hash={text:"merge "+t,token:"merge "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["branch abc"]};throw e}else if(s===void 0||!s){let e=new Error('Incorrect usage of "merge". Current branch ('+d+")has no commits");e.hash={text:"merge "+t,token:"merge "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["commit"]};throw e}else if(g[t]===void 0){let e=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") does not exist");e.hash={text:"merge "+t,token:"merge "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["branch "+t]};throw e}else if(a===void 0||!a){let e=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") has no commits");e.hash={text:"merge "+t,token:"merge "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:['"commit"']};throw e}else if(s===a){let e=new Error('Incorrect usage of "merge". Both branches have same head');e.hash={text:"merge "+t,token:"merge "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["branch abc"]};throw e}else if(e&&f[e]!==void 0){let i=new Error('Incorrect usage of "merge". Commit with id:'+e+" already exists, use different custom Id");i.hash={text:"merge "+t+e+r+n,token:"merge "+t+e+r+n,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["merge "+t+" "+e+"_UNIQUE "+r+" "+n]};throw i}const c={id:e?e:$+"-"+x(),message:"merged branch "+t+" into "+d,seq:$++,parents:[p==null?null:p.id,g[t]],branch:d,type:q.MERGE,customType:r,customId:e?true:false,tag:n?n:""};p=c;f[c.id]=c;g[d]=c.id;i.l.debug(g);i.l.debug("in mergeBranch")};const I=function(t,e,r,n){i.l.debug("Entering cherryPick:",t,e,r);t=i.e.sanitizeText(t,(0,i.c)());e=i.e.sanitizeText(e,(0,i.c)());r=i.e.sanitizeText(r,(0,i.c)());n=i.e.sanitizeText(n,(0,i.c)());if(!t||f[t]===void 0){let r=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');r.hash={text:"cherryPick "+t+" "+e,token:"cherryPick "+t+" "+e,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["cherry-pick abc"]};throw r}let s=f[t];let a=s.branch;if(n&&!(Array.isArray(s.parents)&&s.parents.includes(n))){let t=new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");throw t}if(s.type===q.MERGE&&!n){let t=new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");throw t}if(!e||f[e]===void 0){if(a===d){let r=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');r.hash={text:"cherryPick "+t+" "+e,token:"cherryPick "+t+" "+e,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["cherry-pick abc"]};throw r}const c=f[g[d]];if(c===void 0||!c){let r=new Error('Incorrect usage of "cherry-pick". Current branch ('+d+")has no commits");r.hash={text:"cherryPick "+t+" "+e,token:"cherryPick "+t+" "+e,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["cherry-pick abc"]};throw r}const o={id:$+"-"+x(),message:"cherry-picked "+s+" into "+d,seq:$++,parents:[p==null?null:p.id,s.id],branch:d,type:q.CHERRY_PICK,tag:r??`cherry-pick:${s.id}${s.type===q.MERGE?`|parent:${n}`:""}`};p=o;f[o.id]=o;g[d]=o.id;i.l.debug(g);i.l.debug("in cherryPick")}};const A=function(t){t=i.e.sanitizeText(t,(0,i.c)());if(g[t]===void 0){let e=new Error('Trying to checkout branch which is not yet created. (Help try using "branch '+t+'")');e.hash={text:"checkout "+t,token:"checkout "+t,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:['"branch '+t+'"']};throw e}else{d=t;const e=g[d];p=f[e]}};function C(t,e,r){const i=t.indexOf(e);if(i===-1){t.push(r)}else{t.splice(i,1,r)}}function O(t){const e=t.reduce(((t,e)=>{if(t.seq>e.seq){return t}return e}),t[0]);let r="";t.forEach((function(t){if(t===e){r+="\t*"}else{r+="\t|"}}));const n=[r,e.id,e.seq];for(let i in g){if(g[i]===e.id){n.push(i)}}i.l.debug(n.join(" "));if(e.parents&&e.parents.length==2){const r=f[e.parents[0]];C(t,e,r);t.push(f[e.parents[1]])}else if(e.parents.length==0){return}else{const r=f[e.parents];C(t,e,r)}t=_(t,(t=>t.id));O(t)}const S=function(){i.l.debug(f);const t=H()[0];O([t])};const P=function(){f={};p=null;let t=(0,i.c)().gitGraph.mainBranchName;let e=(0,i.c)().gitGraph.mainBranchOrder;g={};g[t]=null;b={};b[t]={name:t,order:e};d=t;$=0;(0,i.t)()};const G=function(){const t=Object.values(b).map(((t,e)=>{if(t.order!==null){return t}return{...t,order:parseFloat(`0.${e}`,10)}})).sort(((t,e)=>t.order-e.order)).map((({name:t})=>({name:t})));return t};const B=function(){return g};const N=function(){return f};const H=function(){const t=Object.keys(f).map((function(t){return f[t]}));t.forEach((function(t){i.l.debug(t.id)}));t.sort(((t,e)=>t.seq-e.seq));return t};const z=function(){return d};const D=function(){return k};const j=function(){return p};const q={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4};const Y={getConfig:()=>(0,i.c)().gitGraph,setDirection:E,setOptions:T,getOptions:v,commit:L,branch:R,merge:M,cherryPick:I,checkout:A,prettyPrint:S,clear:P,getBranchesAsObjArray:G,getBranches:B,getCommits:N,getCommitsArray:H,getCurrentBranch:z,getDirection:D,getHead:j,setAccTitle:i.s,getAccTitle:i.g,getAccDescription:i.a,setAccDescription:i.b,setDiagramTitle:i.q,getDiagramTitle:i.r,commitType:q};let K={};const F={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4};const U=8;let V={};let W={};let J=[];let Q=0;let X="LR";const Z=()=>{V={};W={};K={};Q=0;J=[];X="LR"};const tt=t=>{const e=document.createElementNS("http://www.w3.org/2000/svg","text");let r=[];if(typeof t==="string"){r=t.split(/\\n|\n|<br\s*\/?>/gi)}else if(Array.isArray(t)){r=t}else{r=[]}for(const i of r){const t=document.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve");t.setAttribute("dy","1em");t.setAttribute("x","0");t.setAttribute("class","row");t.textContent=i.trim();e.appendChild(t)}return e};const et=(t,e,r)=>{const n=(0,i.c)().gitGraph;const s=t.append("g").attr("class","commit-bullets");const a=t.append("g").attr("class","commit-labels");let c=0;if(X==="TB"){c=30}const o=Object.keys(e);const l=o.sort(((t,r)=>e[t].seq-e[r].seq));l.forEach((t=>{const i=e[t];const o=X==="TB"?c+10:V[i.branch].pos;const l=X==="TB"?V[i.branch].pos:c+10;if(r){let t;let e=i.customType!==void 0&&i.customType!==""?i.customType:i.type;switch(e){case F.NORMAL:t="commit-normal";break;case F.REVERSE:t="commit-reverse";break;case F.HIGHLIGHT:t="commit-highlight";break;case F.MERGE:t="commit-merge";break;case F.CHERRY_PICK:t="commit-cherry-pick";break;default:t="commit-normal"}if(e===F.HIGHLIGHT){const e=s.append("rect");e.attr("x",l-10);e.attr("y",o-10);e.attr("height",20);e.attr("width",20);e.attr("class",`commit ${i.id} commit-highlight${V[i.branch].index%U} ${t}-outer`);s.append("rect").attr("x",l-6).attr("y",o-6).attr("height",12).attr("width",12).attr("class",`commit ${i.id} commit${V[i.branch].index%U} ${t}-inner`)}else if(e===F.CHERRY_PICK){s.append("circle").attr("cx",l).attr("cy",o).attr("r",10).attr("class",`commit ${i.id} ${t}`);s.append("circle").attr("cx",l-3).attr("cy",o+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${i.id} ${t}`);s.append("circle").attr("cx",l+3).attr("cy",o+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${i.id} ${t}`);s.append("line").attr("x1",l+3).attr("y1",o+1).attr("x2",l).attr("y2",o-5).attr("stroke","#fff").attr("class",`commit ${i.id} ${t}`);s.append("line").attr("x1",l-3).attr("y1",o+1).attr("x2",l).attr("y2",o-5).attr("stroke","#fff").attr("class",`commit ${i.id} ${t}`)}else{const r=s.append("circle");r.attr("cx",l);r.attr("cy",o);r.attr("r",i.type===F.MERGE?9:10);r.attr("class",`commit ${i.id} commit${V[i.branch].index%U}`);if(e===F.MERGE){const e=s.append("circle");e.attr("cx",l);e.attr("cy",o);e.attr("r",6);e.attr("class",`commit ${t} ${i.id} commit${V[i.branch].index%U}`)}if(e===F.REVERSE){const e=s.append("path");e.attr("d",`M ${l-5},${o-5}L${l+5},${o+5}M${l-5},${o+5}L${l+5},${o-5}`).attr("class",`commit ${t} ${i.id} commit${V[i.branch].index%U}`)}}}if(X==="TB"){W[i.id]={x:l,y:c+10}}else{W[i.id]={x:c+10,y:o}}if(r){const t=4;const e=2;if(i.type!==F.CHERRY_PICK&&(i.customId&&i.type===F.MERGE||i.type!==F.MERGE)&&n.showCommitLabel){const r=a.append("g");const s=r.insert("rect").attr("class","commit-label-bkg");const h=r.append("text").attr("x",c).attr("y",o+25).attr("class","commit-label").text(i.id);let m=h.node().getBBox();s.attr("x",c+10-m.width/2-e).attr("y",o+13.5).attr("width",m.width+2*e).attr("height",m.height+2*e);if(X==="TB"){s.attr("x",l-(m.width+4*t+5)).attr("y",o-12);h.attr("x",l-(m.width+4*t)).attr("y",o+m.height-12)}if(X!=="TB"){h.attr("x",c+10-m.width/2)}if(n.rotateCommitLabel){if(X==="TB"){h.attr("transform","rotate(-45, "+l+", "+o+")");s.attr("transform","rotate(-45, "+l+", "+o+")")}else{let t=-7.5-(m.width+10)/25*9.5;let e=10+m.width/25*8.5;r.attr("transform","translate("+t+", "+e+") rotate(-45, "+c+", "+o+")")}}}if(i.tag){const r=a.insert("polygon");const n=a.append("circle");const s=a.append("text").attr("y",o-16).attr("class","tag-label").text(i.tag);let h=s.node().getBBox();s.attr("x",c+10-h.width/2);const m=h.height/2;const u=o-19.2;r.attr("class","tag-label-bkg").attr("points",`\n          ${c-h.width/2-t/2},${u+e}\n          ${c-h.width/2-t/2},${u-e}\n          ${c+10-h.width/2-t},${u-m-e}\n          ${c+10+h.width/2+t},${u-m-e}\n          ${c+10+h.width/2+t},${u+m+e}\n          ${c+10-h.width/2-t},${u+m+e}`);n.attr("cx",c-h.width/2+t/2).attr("cy",u).attr("r",1.5).attr("class","tag-hole");if(X==="TB"){r.attr("class","tag-label-bkg").attr("points",`\n            ${l},${c+e}\n            ${l},${c-e}\n            ${l+10},${c-m-e}\n            ${l+10+h.width+t},${c-m-e}\n            ${l+10+h.width+t},${c+m+e}\n            ${l+10},${c+m+e}`).attr("transform","translate(12,12) rotate(45, "+l+","+c+")");n.attr("cx",l+t/2).attr("cy",c).attr("transform","translate(12,12) rotate(45, "+l+","+c+")");s.attr("x",l+5).attr("y",c+3).attr("transform","translate(14,14) rotate(45, "+l+","+c+")")}}}c+=50;if(c>Q){Q=c}}))};const rt=(t,e,r,i,n)=>{const s=X==="TB"?r.x<i.x:r.y<i.y;const a=s?e.branch:t.branch;const c=t=>t.branch===a;const o=r=>r.seq>t.seq&&r.seq<e.seq;return Object.values(n).some((t=>o(t)&&c(t)))};const it=(t,e,r=0)=>{const i=t+Math.abs(t-e)/2;if(r>5){return i}let n=J.every((t=>Math.abs(t-i)>=10));if(n){J.push(i);return i}const s=Math.abs(t-e);return it(t,e-s/5,r+1)};const nt=(t,e,r,i)=>{const n=W[e.id];const s=W[r.id];const a=rt(e,r,n,s,i);let c="";let o="";let l=0;let h=0;let m=V[r.branch].index;let u;if(a){c="A 10 10, 0, 0, 0,";o="A 10 10, 0, 0, 1,";l=10;h=10;const t=n.y<s.y?it(n.y,s.y):it(s.y,n.y);const i=n.x<s.x?it(n.x,s.x):it(s.x,n.x);if(X==="TB"){if(n.x<s.x){m=V[r.branch].index;u=`M ${n.x} ${n.y} L ${i-l} ${n.y} ${o} ${i} ${n.y+h} L ${i} ${s.y-l} ${c} ${i+h} ${s.y} L ${s.x} ${s.y}`}else{m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${i+l} ${n.y} ${c} ${i} ${n.y+h} L ${i} ${s.y-l} ${o} ${i-h} ${s.y} L ${s.x} ${s.y}`}}else{if(n.y<s.y){m=V[r.branch].index;u=`M ${n.x} ${n.y} L ${n.x} ${t-l} ${c} ${n.x+h} ${t} L ${s.x-l} ${t} ${o} ${s.x} ${t+h} L ${s.x} ${s.y}`}else{m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${n.x} ${t+l} ${o} ${n.x+h} ${t} L ${s.x-l} ${t} ${c} ${s.x} ${t-h} L ${s.x} ${s.y}`}}}else{if(X==="TB"){if(n.x<s.x){c="A 20 20, 0, 0, 0,";o="A 20 20, 0, 0, 1,";l=20;h=20;m=V[r.branch].index;u=`M ${n.x} ${n.y} L ${s.x-l} ${n.y} ${o} ${s.x} ${n.y+h} L ${s.x} ${s.y}`}if(n.x>s.x){c="A 20 20, 0, 0, 0,";o="A 20 20, 0, 0, 1,";l=20;h=20;m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${n.x} ${s.y-l} ${o} ${n.x-h} ${s.y} L ${s.x} ${s.y}`}if(n.x===s.x){m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${n.x+l} ${n.y} ${c} ${n.x+h} ${s.y+l} L ${s.x} ${s.y}`}}else{if(n.y<s.y){c="A 20 20, 0, 0, 0,";l=20;h=20;m=V[r.branch].index;u=`M ${n.x} ${n.y} L ${n.x} ${s.y-l} ${c} ${n.x+h} ${s.y} L ${s.x} ${s.y}`}if(n.y>s.y){c="A 20 20, 0, 0, 0,";l=20;h=20;m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${s.x-l} ${n.y} ${c} ${s.x} ${n.y-h} L ${s.x} ${s.y}`}if(n.y===s.y){m=V[e.branch].index;u=`M ${n.x} ${n.y} L ${n.x} ${s.y-l} ${c} ${n.x+h} ${s.y} L ${s.x} ${s.y}`}}}t.append("path").attr("d",u).attr("class","arrow arrow"+m%U)};const st=(t,e)=>{const r=t.append("g").attr("class","commit-arrows");Object.keys(e).forEach((t=>{const i=e[t];if(i.parents&&i.parents.length>0){i.parents.forEach((t=>{nt(r,e[t],i,e)}))}}))};const at=(t,e)=>{const r=(0,i.c)().gitGraph;const n=t.append("g");e.forEach(((t,e)=>{const i=e%U;const s=V[t.name].pos;const a=n.append("line");a.attr("x1",0);a.attr("y1",s);a.attr("x2",Q);a.attr("y2",s);a.attr("class","branch branch"+i);if(X==="TB"){a.attr("y1",30);a.attr("x1",s);a.attr("y2",Q);a.attr("x2",s)}J.push(s);let c=t.name;const o=tt(c);const l=n.insert("rect");const h=n.insert("g").attr("class","branchLabel");const m=h.insert("g").attr("class","label branch-label"+i);m.node().appendChild(o);let u=o.getBBox();l.attr("class","branchLabelBkg label"+i).attr("rx",4).attr("ry",4).attr("x",-u.width-4-(r.rotateCommitLabel===true?30:0)).attr("y",-u.height/2+8).attr("width",u.width+18).attr("height",u.height+4);m.attr("transform","translate("+(-u.width-14-(r.rotateCommitLabel===true?30:0))+", "+(s-u.height/2-1)+")");if(X==="TB"){l.attr("x",s-u.width/2-10).attr("y",0);m.attr("transform","translate("+(s-u.width/2-5)+", 0)")}if(X!=="TB"){l.attr("transform","translate(-19, "+(s-u.height/2)+")")}}))};const ct=function(t,e,r,s){Z();const a=(0,i.c)();const c=a.gitGraph;i.l.debug("in gitgraph renderer",t+"\n","id:",e,r);K=s.db.getCommits();const o=s.db.getBranchesAsObjArray();X=s.db.getDirection();const l=(0,n.Ltv)(`[id="${e}"]`);let h=0;o.forEach(((t,e)=>{const r=tt(t.name);const i=l.append("g");const n=i.insert("g").attr("class","branchLabel");const s=n.insert("g").attr("class","label branch-label");s.node().appendChild(r);let a=r.getBBox();V[t.name]={pos:h,index:e};h+=50+(c.rotateCommitLabel?40:0)+(X==="TB"?a.width/2:0);s.remove();n.remove();i.remove()}));et(l,K,false);if(c.showBranches){at(l,o)}st(l,K);et(l,K,true);i.u.insertTitle(l,"gitTitleText",c.titleTopMargin,s.db.getDiagramTitle());(0,i.y)(void 0,l,c.diagramPadding,c.useMaxWidth??a.useMaxWidth)};const ot={draw:ct};const lt=t=>`\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0,1,2,3,4,5,6,7].map((e=>`\n        .branch-label${e} { fill: ${t["gitBranchLabel"+e]}; }\n        .commit${e} { stroke: ${t["git"+e]}; fill: ${t["git"+e]}; }\n        .commit-highlight${e} { stroke: ${t["gitInv"+e]}; fill: ${t["gitInv"+e]}; }\n        .label${e}  { fill: ${t["git"+e]}; }\n        .arrow${e} { stroke: ${t["git"+e]}; }\n        `)).join("\n")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${t.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${t.commitLabelFontSize}; fill: ${t.commitLabelColor};}\n  .commit-label-bkg { font-size: ${t.commitLabelFontSize}; fill: ${t.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${t.tagLabelFontSize}; fill: ${t.tagLabelColor};}\n  .tag-label-bkg { fill: ${t.tagLabelBackground}; stroke: ${t.tagLabelBorder}; }\n  .tag-hole { fill: ${t.textColor}; }\n\n  .commit-merge {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${t.textColor};\n  }\n`;const ht=lt;const mt={parser:m,db:Y,renderer:ot,styles:ht}}}]);