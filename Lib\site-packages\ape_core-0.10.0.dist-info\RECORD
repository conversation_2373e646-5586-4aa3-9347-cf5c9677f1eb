ape/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ape/core/__pycache__/__init__.cpython-313.pyc,,
ape/core/core_prompts/__init__.py,sha256=sN3Jv_GCCsfF2rKaELCYeBDsVrNCiSUj0l3_pd3BAOM,321
ape/core/core_prompts/__pycache__/__init__.cpython-313.pyc,,
ape/core/core_prompts/dataset-descriptor-with-prior-observations.prompt,sha256=aUhKsgO8nmZgPtpM7dU9woW-Q64r-N4Uq44tmWQFTm0,1518
ape/core/core_prompts/dataset-descriptor.prompt,sha256=TeAoj_UaEv1l1XXRm80WbjWtMst37TWqNq0JTuskn78,1610
ape/core/core_prompts/describe-prompt.prompt,sha256=PlrtQizJJr1-dh_A2PFhIOT-bs-cCsMUf_C8AfA0XOo,913
ape/core/core_prompts/evoprompt-prompt-de.prompt,sha256=NJgE70jFj5V9rMlF1rSh0w6GM1XFnB7atHXA6OyRSRY,5272
ape/core/core_prompts/evoprompt-prompt-ga.prompt,sha256=WkKNBQTYvaTJ-Hep7K2NHnGzcRVaLBUeRJvr5xPH90M,3852
ape/core/core_prompts/evoprompt-prompt-para.prompt,sha256=Q3aKUyv6Gt6bCAAqrAwInQ_HbKNqStT1ctncRlmGLts,1884
ape/core/core_prompts/expel-failure-feedback-generator.prompt,sha256=fomvHU1uw2_lqB4dy66JXMKZ0n7CUMeDymPJQH3Bf0g,2411
ape/core/core_prompts/expel-feedback-applier.prompt,sha256=T7_h98VlR-rbqkBqJZ_EAYcDEOcWGjueVTH53Q21tMI,3490
ape/core/core_prompts/expel-success-feedback-generator.prompt,sha256=GmG-N6_urFBOkD5qNX8bnZmrSMMdvF89xUHuAL-VKSw,2145
ape/core/core_prompts/gen-fewshot-placeholder.prompt,sha256=sTx5SmYiqVf16If-vr-dItY8KkN47iaIx_VfhR2MLds,1515
ape/core/core_prompts/gen-instruction-seeds.prompt,sha256=xAUucBoQBJWE6pqc5PBpNZF9PTWGRa44HzV_lIoKYHc,1160
ape/core/core_prompts/gen-instruction-with-eval.prompt,sha256=ZdGiqA-YvE8GdVoJ98hsUg-BGR13obTK0Gi9PCi__A4,3119
ape/core/core_prompts/gen-instructions.prompt,sha256=oP-Y89dT6ICro-ZiBh2ZOkOIsPCy8xpO0kIYeXYoDwg,2530
ape/core/core_prompts/gen-merged-prompt.prompt,sha256=Jl7Q0p21qzN18KUcmvXITYoQ9kwXcEukfFM0DdUh_G8,2234
ape/core/core_prompts/gen-metric-description-with-global-metric.prompt,sha256=GOnDY_WVSXrBmJux6rRDOkzXuA2Lu_EVSBhW3Fv1vIk,8869
ape/core/core_prompts/gen-metric-description.prompt,sha256=m8iCq4PTe2LaH-1Tv0y3mm2wKY0UtOp_Loeyz0GTNbA,8460
ape/core/core_prompts/observation-summarizer.prompt,sha256=AUjlK9Y0zGS8WLq8lfGfNwsRtT4f-S9cmL9aw5iXvbs,911
ape/core/core_prompts/reformat-prompt-json-object.prompt,sha256=RGsg_WEqQbpoL7ChyiM2FwOvNW393LxwHS60ODVsV54,2510
ape/core/core_prompts/reformat-prompt-json-schema.prompt,sha256=7HGANR9z5gFHIYfhf_AxXJjDldQYmW7iEkZ6RZDH5HE,2592
ape/core/core_prompts/text-grad-evo-applier.prompt,sha256=EmDqlj9vamewyP7bt8udEXvYW1KLpCfU_USsE8eCm3Y,2450
ape/core/core_prompts/text-grad-evo-generator.prompt,sha256=Rr0BKhH9YgImjhenbVREeXgbd0H6eoEXXyKp5CjktWA,1837
ape/core/core_prompts/text-gradient-applier.prompt,sha256=h-Zfq93YI9urqFHhM7vBrOv61ra672j09mRYfjnXiNQ,2984
ape/core/core_prompts/text-gradient-generator.prompt,sha256=-VM4BI2cAgFpHhsjlHd0rZ5dzaYMzG-Re-nCumTrUfI,1989
ape/core/trainer/__init__.py,sha256=j5kR6BHwHJGjrKB_WcV4UAJPebaF7cjAHLIETqZtAwo,500
ape/core/trainer/__pycache__/__init__.cpython-313.pyc,,
ape/core/trainer/__pycache__/base.cpython-313.pyc,,
ape/core/trainer/base.py,sha256=dD428QGu2TbiysONpei8jgOiNnfB2P8KUikyK1EkyK0,8684
ape/core/trainer/community/EXAMPLE_README.md,sha256=tbudCkkbto-_vCArxRc7tWIgObrY69P0_9-mawC7NoI,399
ape/core/trainer/community/expel/README.md,sha256=utDoLKZdJ8rP51LpphsHOsglcdOKYS5SjcCY4voiPkc,2336
ape/core/trainer/community/expel/__pycache__/expel_trainer.cpython-313.pyc,,
ape/core/trainer/community/expel/expel_trainer.py,sha256=9KYtHKR4qUL9fFChnp-yYXcSSUFlLf4hJRguPYVXamU,18362
ape/core/trainer/community/few_shot/README.md,sha256=9D7oRTjpMaYM9fCvj269eUYsGw1vk1VK0iGuR2D6q18,1998
ape/core/trainer/community/few_shot/__pycache__/fewshot_trainer.cpython-313.pyc,,
ape/core/trainer/community/few_shot/fewshot_trainer.py,sha256=2dsLdNx1wBuQ7sDogWxb8943fatUC9hDQ5sPWaodq7U,7526
ape/core/trainer/community/optuna/README.md,sha256=AKKiPUnbfWHtU9-jXPjE-9WIMiRHgYVPGe7Tg5wZ0CA,1701
ape/core/trainer/community/optuna/__pycache__/optuna_trainer.cpython-313.pyc,,
ape/core/trainer/community/optuna/optuna_trainer.py,sha256=ILegqttHmW-nBWB4N0GjLFtVvwuhBXzgHvtx6LJ5h84,13762
ape/core/trainer/community/text_grad_evo/README.md,sha256=N4-_sKWz1S6hhKVdmX0FrFX2B2Q2VjI-gJTrCGJ9608,2623
ape/core/trainer/community/text_grad_evo/__pycache__/text_grad_evo_trainer.cpython-313.pyc,,
ape/core/trainer/community/text_grad_evo/text_grad_evo_trainer.py,sha256=rt3pH_QDzPr64wd2J_PIxI1xGcBU0QNDyiwn3BxcFow,20458
ape/core/trainer/community/text_gradient/README.md,sha256=tabffWEYehTs4fEKwof7xhvvlwt5KzFPhI6D76eNl8o,2856
ape/core/trainer/community/text_gradient/__pycache__/text_gradient_trainer.cpython-313.pyc,,
ape/core/trainer/community/text_gradient/text_gradient_trainer.py,sha256=btM249bU6sa0q2Y1kM9aWblFJ6MgVXwRSnbPCH14p78,12966
ape/core/trainer/papers/EXAMPLE_README.md,sha256=A20RUZjC5CzUoy4Ah3lpqK7s4_Ue0Q0NAOaIP7gSdA4,409
ape/core/trainer/papers/dspy_mipro/README.md,sha256=2nhtOrE_v7ZoD0J6PyM7Ty4H_-kzJeuJbowF7E3uCzs,3655
ape/core/trainer/papers/dspy_mipro/__pycache__/dspy_mipro_trainer.cpython-313.pyc,,
ape/core/trainer/papers/dspy_mipro/dspy_mipro_trainer.py,sha256=otmP-8rzKiAM0_ju2ATFe8msH1QraPNUx-UUqvPFWgw,16541
ape/core/trainer/papers/evo_prompt/README.md,sha256=gJ87RUd7G2SQ1B4UomKYaEqi9-6Za7dRx5mhsaeuM84,1662
ape/core/trainer/papers/evo_prompt/__pycache__/evo_prompt_trainer.cpython-313.pyc,,
ape/core/trainer/papers/evo_prompt/evo_prompt_trainer.py,sha256=EKosq4AgtRNegfn9N5R7-LexkDPU4QKF72NVLCKV9ic,16362
ape/core/types/__init__.py,sha256=Vmf4kNrLrN0OcK7OaAph73HrDBaF1_cfuT-u12PEw2k,21
ape/core/types/__pycache__/__init__.cpython-313.pyc,,
ape/core/types/__pycache__/report.cpython-313.pyc,,
ape/core/types/report.py,sha256=LFk_EGzTIKh_nHJIjfZtvEx8UJCwAjqTB3gsHgpJKoo,634
ape/core/utils/__init__.py,sha256=L0LuEbP5LT1bqjFY96QUybPP-xjS7KLhJAgwyuJJ8oM,3995
ape/core/utils/__pycache__/__init__.cpython-313.pyc,,
ape_core-0.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ape_core-0.10.0.dist-info/METADATA,sha256=nN5GEwuWZO-MWkAajnkYLkVVB--vGHwyK0Uqp78orxk,857
ape_core-0.10.0.dist-info/RECORD,,
ape_core-0.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ape_core-0.10.0.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
ape_core-0.10.0.dist-info/top_level.txt,sha256=MP0mG0BS8tqqlw-z7DrBYqjo0s5BSvKX3Lt2aeZL5lo,4
