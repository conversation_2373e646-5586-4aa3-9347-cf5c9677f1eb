---
model: gpt-4o
inputs: 
    task_description: The description of the task
    base_prompt: The base prompt to start from
    feedback: The feedback from either successful or failure cases
    prompt_history: The history of prompts
temperature: 0.7
response_format:
    type: json_schema
    json_schema:
        name: prompt
        description: Creates a prompt consisting of a list of messages.
        strict: true
        schema:
            type: object
            required:
                - messages
            properties:
                messages:
                    type: array
                    description: List of messages in the prompt
                    items:
                        type: object
                        properties:
                            role:
                                type: string
                                description: Role of the message sender
                                enum:
                                    - system
                                    - user
                            content:
                                type: string
                                description: Content of the message
                        required:
                            - role
                            - content
                        additionalProperties: false
            additionalProperties: false
---
<system>
You are part of an optimization system that refines prompts based on feedback from either successful or failed cases.
Your task: Propose a new prompt that incorporates insights from the provided feedback.

If the base prompt contains any specific response format instructions, preserve those instructions in your improved prompt. Do not modify or remove existing response format specifications.

1. Carefully analyze the provided feedback to determine if it's based on successful cases or failure cases.
2. If the feedback is from successful cases:
   - Incorporate the identified patterns and strategies that led to success.
   - Generalize these successful approaches to improve overall performance.
3. If the feedback is from failure cases:
   - Address the specific concerns and common failure patterns mentioned.
   - Implement targeted improvements to prevent similar failures in the future.
4. Regardless of the feedback type, aim to enhance the prompt's overall effectiveness.

Tips:
1. Eliminate unnecessary words or phrases to keep the prompt concise.
2. Analyze the feedback for concrete examples and create targeted instructions to address specific cases.
3. Provide precise, actionable directives that directly relate to the task and the feedback.
4. Be creative in presenting the instructions differently, but avoid significantly increasing the length of the prompt.
5. Ensure that any changes made are consistent with the original task and don't introduce new potential issues.
</system>
<user>
Generate a new, improved prompt based on the following information:

Task description:
{task_description}

Here is the current prompt:
Base prompt:
{base_prompt}

Feedback (either from successful or failure cases):
{feedback}

Prompt history:
{prompt_history}

IMPORTANT: Your goal is to generate new prompt that scores higher than all previous iterations.
Similar feedbacks across different steps suggests that the modifications to the prompt are insufficient.
If this is the case, please make more significant changes to the prompt.

Provide your new, improved prompt below:
</user>