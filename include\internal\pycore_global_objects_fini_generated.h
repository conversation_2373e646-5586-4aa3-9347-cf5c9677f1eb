#ifndef Py_INTERNAL_GLOBAL_OBJECTS_FINI_GENERATED_INIT_H
#define Py_INTERNAL_GLOBAL_OBJECTS_FINI_GENERATED_INIT_H
#ifdef __cplusplus
extern "C" {
#endif

#ifndef Py_BUILD_CORE
#  error "this header requires Py_BUILD_CORE define"
#endif

#ifdef Py_DEBUG
static inline void
_PyStaticObject_CheckRefcnt(PyObject *obj) {
    if (Py_REFCNT(obj) < _Py_IMMORTAL_REFCNT) {
        fprintf(stderr, "Immortal Object has less refcnt than expected.\n");
        _PyObject_Dump(obj);
    }
}
#endif

/* The following is auto-generated by Tools/build/generate_global_objects.py. */
#ifdef Py_DEBUG
static inline void
_PyStaticObjects_CheckRefcnt(PyInterpreterState *interp) {
    /* generated runtime-global */
    // (see pycore_runtime_init_generated.h)
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + -5]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + -4]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + -3]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + -2]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + -1]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 0]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 1]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 2]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 3]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 4]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 5]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 6]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 7]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 8]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 9]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 10]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 11]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 12]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 13]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 14]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 15]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 16]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 17]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 18]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 19]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 20]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 21]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 22]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 23]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 24]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 25]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 26]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 27]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 28]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 29]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 30]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 31]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 32]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 33]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 34]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 35]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 36]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 37]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 38]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 39]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 40]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 41]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 42]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 43]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 44]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 45]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 46]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 47]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 48]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 49]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 50]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 51]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 52]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 53]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 54]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 55]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 56]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 57]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 58]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 59]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 60]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 61]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 62]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 63]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 64]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 65]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 66]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 67]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 68]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 69]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 70]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 71]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 72]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 73]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 74]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 75]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 76]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 77]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 78]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 79]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 80]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 81]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 82]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 83]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 84]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 85]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 86]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 87]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 88]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 89]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 90]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 91]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 92]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 93]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 94]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 95]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 96]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 97]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 98]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 99]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 100]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 101]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 102]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 103]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 104]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 105]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 106]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 107]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 108]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 109]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 110]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 111]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 112]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 113]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 114]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 115]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 116]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 117]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 118]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 119]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 120]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 121]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 122]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 123]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 124]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 125]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 126]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 127]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 129]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 130]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 131]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 132]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 133]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 134]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 135]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 136]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 137]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 138]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 139]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 140]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 141]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 142]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 143]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 144]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 145]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 146]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 147]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 148]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 149]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 150]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 151]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 152]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 153]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 154]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 155]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 156]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 157]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 158]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 159]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 160]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 161]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 162]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 163]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 164]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 165]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 166]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 167]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 168]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 169]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 170]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 171]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 172]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 173]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 174]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 175]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 176]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 177]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 178]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 179]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 180]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 181]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 182]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 183]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 184]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 185]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 186]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 187]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 188]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 189]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 190]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 191]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 192]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 193]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 194]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 195]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 196]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 197]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 198]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 199]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 200]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 201]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 202]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 203]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 204]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 205]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 206]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 207]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 208]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 209]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 210]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 211]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 212]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 213]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 214]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 215]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 216]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 217]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 218]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 219]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 220]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 221]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 222]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 223]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 224]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 225]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 226]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 227]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 228]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 229]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 230]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 231]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 232]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 233]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 234]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 235]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 236]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 237]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 238]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 239]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 240]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 241]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 242]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 243]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 244]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 245]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 246]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 247]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 248]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 249]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 250]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 251]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 252]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 253]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 254]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 255]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(small_ints)[_PY_NSMALLNEGINTS + 256]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[0]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[1]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[2]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[3]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[4]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[5]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[6]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[7]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[8]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[9]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[10]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[11]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[12]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[13]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[14]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[15]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[16]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[17]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[18]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[19]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[20]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[21]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[22]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[23]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[24]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[25]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[26]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[27]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[28]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[29]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[30]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[31]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[32]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[33]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[34]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[35]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[36]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[37]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[38]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[39]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[40]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[41]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[42]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[43]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[44]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[45]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[46]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[47]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[48]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[49]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[50]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[51]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[52]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[53]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[54]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[55]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[56]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[57]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[58]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[59]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[60]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[61]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[62]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[63]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[64]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[65]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[66]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[67]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[68]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[69]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[70]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[71]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[72]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[73]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[74]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[75]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[76]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[77]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[78]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[79]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[80]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[81]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[82]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[83]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[84]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[85]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[86]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[87]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[88]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[89]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[90]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[91]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[92]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[93]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[94]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[95]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[96]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[97]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[98]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[99]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[100]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[101]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[102]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[103]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[104]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[105]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[106]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[107]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[108]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[109]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[110]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[111]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[112]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[113]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[114]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[115]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[116]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[117]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[118]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[119]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[120]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[121]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[122]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[123]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[124]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[125]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[126]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[127]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[129]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[130]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[131]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[132]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[133]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[134]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[135]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[136]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[137]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[138]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[139]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[140]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[141]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[142]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[143]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[144]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[145]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[146]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[147]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[148]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[149]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[150]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[151]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[152]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[153]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[154]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[155]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[156]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[157]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[158]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[159]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[160]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[161]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[162]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[163]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[164]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[165]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[166]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[167]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[168]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[169]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[170]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[171]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[172]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[173]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[174]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[175]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[176]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[177]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[178]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[179]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[180]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[181]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[182]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[183]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[184]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[185]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[186]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[187]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[188]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[189]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[190]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[191]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[192]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[193]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[194]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[195]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[196]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[197]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[198]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[199]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[200]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[201]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[202]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[203]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[204]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[205]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[206]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[207]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[208]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[209]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[210]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[211]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[212]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[213]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[214]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[215]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[216]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[217]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[218]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[219]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[220]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[221]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[222]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[223]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[224]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[225]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[226]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[227]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[228]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[229]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[230]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[231]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[232]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[233]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[234]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[235]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[236]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[237]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[238]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[239]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[240]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[241]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[242]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[243]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[244]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[245]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[246]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[247]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[248]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[249]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[250]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[251]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[252]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[253]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[254]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_characters)[255]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_dictcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_genexpr));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_lambda));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_listcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_module));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_null));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_setcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_string));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(anon_unknown));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(dbl_close_br));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(dbl_open_br));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(dbl_percent));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(defaults));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(dot_locals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(empty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(generic_base));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(json_decoder));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(kwdefaults));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(list_err));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(str_replace_inf));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(type_params));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_STR(utf_8));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(CANCELLED));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(FINISHED));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(False));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(JSONDecodeError));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(PENDING));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(Py_Repr));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(TextIOWrapper));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(True));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(WarningMessage));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_WindowsConsoleIO));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__IOBase_closed));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__abc_tpflags__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__abs__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__abstractmethods__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__add__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__aenter__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__aexit__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__aiter__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__all__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__and__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__anext__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__annotations__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__args__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__await__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__bases__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__bool__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__buffer__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__build_class__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__builtins__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__bytes__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__call__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__cantrace__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__class__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__class_getitem__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__classcell__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__classdict__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__classdictcell__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__complex__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__contains__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__copy__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ctypes_from_outparam__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__del__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__delattr__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__delete__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__delitem__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__dict__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__dictoffset__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__dir__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__divmod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__doc__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__enter__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__eq__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__exit__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__file__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__firstlineno__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__float__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__floordiv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__format__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__fspath__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ge__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__get__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getattr__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getattribute__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getinitargs__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getitem__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getnewargs__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getnewargs_ex__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__getstate__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__gt__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__hash__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__iadd__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__iand__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ifloordiv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ilshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__imatmul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__imod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__import__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__imul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__index__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__init__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__init_subclass__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__instancecheck__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__int__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__invert__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ior__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ipow__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__irshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__isabstractmethod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__isub__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__iter__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__itruediv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ixor__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__le__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__len__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__length_hint__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__lltrace__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__loader__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__lshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__lt__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__main__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__match_args__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__matmul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__missing__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__mod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__module__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__mro_entries__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__mul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__name__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ne__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__neg__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__new__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__newobj__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__newobj_ex__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__next__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__notes__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__or__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__orig_class__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__origin__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__package__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__parameters__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__path__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__pos__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__pow__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__prepare__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__qualname__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__radd__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rand__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rdivmod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__reduce__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__reduce_ex__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__release_buffer__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__repr__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__reversed__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rfloordiv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rlshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rmatmul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rmod__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rmul__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__ror__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__round__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rpow__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rrshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rshift__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rsub__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rtruediv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__rxor__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__set__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__set_name__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__setattr__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__setitem__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__setstate__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__sizeof__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__slotnames__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__slots__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__spec__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__static_attributes__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__str__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__sub__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__subclasscheck__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__subclasshook__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__truediv__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__trunc__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__type_params__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__typing_is_unpacked_typevartuple__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__typing_prepare_subst__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__typing_subst__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__typing_unpacked_tuple_args__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__warningregistry__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__weaklistoffset__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__weakref__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(__xor__));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_abc_impl));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_abstract_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_active));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_align_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_annotation));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_anonymous_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_argtypes_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_as_parameter_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_asyncio_future_blocking));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_blksize));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_bootstrap));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_check_retval_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_dealloc_warn));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_feature_version));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_field_types));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_fields_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_finalizing));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_find_and_load));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_fix_up_module));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_flags_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_get_sourcefile));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_handle_fromlist));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_initializing));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_io));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_is_text_encoding));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_length_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_limbo));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_lock_unlock_module));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_loop));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_needs_com_addref_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_only_immortal));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_pack_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_restype_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_showwarnmsg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_shutdown));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_slotnames));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_strptime));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_strptime_datetime));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_swappedbytes_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_type_));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_uninitialized_submodules));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_warn_unawaited_coroutine));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(_xoptions));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(abs_tol));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(access));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(aclose));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(add));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(add_done_callback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(after_in_child));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(after_in_parent));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(aggregate_class));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(alias));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(allow_code));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(append));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(arg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(argdefs));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(args));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(arguments));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(argv));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(as_integer_ratio));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(asend));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ast));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(athrow));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(attribute));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(authorizer_callback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(autocommit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(backtick));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(base));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(before));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(big));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(binary_form));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(block));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(bound));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(buffer));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(buffer_callback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(buffer_size));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(buffering));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(buffers));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(bufsize));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(builtins));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(byteorder));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(bytes));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(bytes_per_sep));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(c_call));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(c_exception));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(c_return));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cached_datetime_module));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cached_statements));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cadata));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cafile));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(call));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(call_exception_handler));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(call_soon));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(callback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cancel));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(capath));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(category));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cb_type));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(certfile));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(check_same_thread));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(clear));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(close));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(closed));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(closefd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(closure));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_argcount));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_cellvars));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_code));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_consts));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_exceptiontable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_filename));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_firstlineno));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_flags));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_freevars));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_kwonlyargcount));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_linetable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_names));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_nlocals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_posonlyargcount));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_qualname));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_stacksize));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(co_varnames));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(code));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(col_offset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(command));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(comment_factory));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(compile_mode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(consts));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(context));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(contravariant));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cookie));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(copy));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(copyreg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(coro));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(count));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(covariant));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(cwd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(data));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(database));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(day));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(decode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(decoder));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(default));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(defaultaction));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(delete));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(depth));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(desired_access));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(detect_types));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(deterministic));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(device));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dict));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dictcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(difference_update));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(digest));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(digest_size));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(digestmod));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dir_fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(discard));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dispatch_table));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(displayhook));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dklen));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(doc));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dont_inherit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dst));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(dst_dir_fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(eager_start));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(effective_ids));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(element_factory));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(encode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(encoding));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(end));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(end_col_offset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(end_lineno));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(end_offset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(endpos));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(entrypoint));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(env));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(errors));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(event));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(eventmask));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(exc_type));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(exc_value));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(excepthook));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(exception));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(existing_file_name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(exp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(extend));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(extra_tokens));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(facility));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(factory));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(false));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(family));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fanout));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fd2));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fdel));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fget));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(file));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(file_actions));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(filename));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fileno));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(filepath));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fillvalue));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(filter));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(filters));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(final));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(find_class));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fix_imports));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(flags));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(flush));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fold));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(follow_symlinks));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(format));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(from_param));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fromlist));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fromtimestamp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fromutc));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(fset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(func));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(future));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(generation));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(genexpr));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(get));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(get_debug));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(get_event_loop));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(get_loop));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(get_source));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(getattr));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(getstate));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(gid));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(globals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(groupindex));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(groups));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(handle));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(handle_seq));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(has_location));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(hash_name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(header));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(headers));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(hi));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(hook));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(hour));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ident));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(identity_hint));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ignore));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(imag));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(importlib));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(in_fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(incoming));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(indexgroup));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(inf));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(infer_variance));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(inherit_handle));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(inheritable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initial));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initial_bytes));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initial_owner));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initial_state));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initial_value));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(initval));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(inner_size));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(input));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(insert_comments));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(insert_pis));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(instructions));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(intern));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(intersection));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(interval));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(is_running));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(isatty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(isinstance));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(isoformat));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(isolation_level));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(istext));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(item));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(items));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(iter));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(iterable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(iterations));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(join));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(jump));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(keepends));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(key));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(keyfile));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(keys));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(kind));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(kw));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(kw1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(kw2));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(kwdefaults));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(label));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(lambda));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last_exc));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last_node));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last_traceback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last_type));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(last_value));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(latin1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(leaf_size));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(len));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(length));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(level));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(limit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(line));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(line_buffering));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(lineno));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(listcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(little));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(lo));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(locale));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(locals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(logoption));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(loop));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(manual_reset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mapping));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(match));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(max_length));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxdigits));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxevents));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxlen));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxmem));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxsplit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(maxvalue));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(memLevel));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(memlimit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(message));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(metaclass));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(metadata));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(method));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(microsecond));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(milliseconds));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(minute));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mod));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(module));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(module_globals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(modules));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(month));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mro));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(msg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mutex));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(mycmp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(n_arg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(n_fields));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(n_sequence_fields));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(n_unnamed_fields));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(name_from));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(namespace_separator));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(namespaces));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(narg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ndigits));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(nested));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(new_file_name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(new_limit));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(newline));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(newlines));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(next));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(nlocals));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(node_depth));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(node_offset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ns));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(nstype));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(nt));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(null));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(number));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(obj));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(object));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(offset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(offset_dst));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(offset_src));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(on_type_read));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(onceregistry));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(only_keys));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(oparg));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(opcode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(open));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(opener));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(operation));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(optimize));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(options));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(order));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(origin));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(out_fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(outgoing));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(overlapped));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(owner));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pages));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(parent));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(password));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(path));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pattern));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(peek));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(persistent_id));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(persistent_load));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(person));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pi_factory));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pid));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(policy));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pos));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pos1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(pos2));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(posix));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(print_file_and_line));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(priority));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(progress));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(progress_handler));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(progress_routine));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(proto));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(protocol));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ps1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(ps2));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(query));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(quotetabs));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(raw));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(read));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(read1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readall));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readinto));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readinto1));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readline));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(readonly));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(real));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reducer_override));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(registry));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(rel_tol));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(release));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reload));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(repl));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(replace));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reserved));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reset));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(resetids));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(return));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reverse));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(reversed));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(salt));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sched_priority));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(scheduler));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(second));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(security_attributes));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(seek));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(seekable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(selectors));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(self));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(send));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sep));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sequence));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(server_hostname));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(server_side));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(session));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setcomp));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setpgroup));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setsid));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setsigdef));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setsigmask));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(setstate));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(shape));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(show_cmd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(signed));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(size));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sizehint));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(skip_file_prefixes));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sleep));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sock));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sort));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(source));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(source_traceback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(spam));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(src));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(src_dir_fd));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(stacklevel));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(start));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(statement));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(status));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(stderr));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(stdin));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(stdout));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(step));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(steps));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(store_name));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(strategy));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(strftime));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(strict));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(strict_mode));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(string));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(sub_key));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(symmetric_difference_update));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tabsize));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tag));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(target));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(target_is_directory));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(task));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tb_frame));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tb_lasti));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tb_lineno));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tb_next));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tell));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(template));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(term));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(text));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(threading));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(throw));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(timeout));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(times));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(timetuple));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(top));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(trace_callback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(traceback));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(trailers));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(translate));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(true));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(truncate));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(twice));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(txt));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(type));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(type_params));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tz));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tzinfo));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(tzname));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(uid));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(unlink));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(unraisablehook));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(uri));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(usedforsecurity));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(value));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(values));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(version));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(volume));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(wait_all));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(warn_on_full_buffer));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(warnings));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(warnoptions));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(wbits));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(week));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(weekday));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(which));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(who));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(withdata));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(writable));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(write));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(write_through));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(year));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_ID(zdict));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[0]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[1]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[2]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[3]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[4]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[5]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[6]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[7]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[8]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[9]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[10]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[11]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[12]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[13]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[14]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[15]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[16]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[17]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[18]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[19]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[20]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[21]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[22]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[23]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[24]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[25]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[26]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[27]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[28]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[29]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[30]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[31]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[32]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[33]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[34]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[35]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[36]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[37]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[38]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[39]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[40]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[41]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[42]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[43]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[44]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[45]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[46]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[47]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[48]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[49]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[50]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[51]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[52]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[53]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[54]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[55]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[56]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[57]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[58]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[59]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[60]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[61]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[62]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[63]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[64]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[65]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[66]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[67]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[68]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[69]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[70]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[71]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[72]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[73]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[74]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[75]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[76]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[77]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[78]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[79]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[80]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[81]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[82]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[83]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[84]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[85]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[86]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[87]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[88]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[89]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[90]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[91]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[92]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[93]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[94]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[95]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[96]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[97]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[98]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[99]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[100]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[101]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[102]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[103]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[104]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[105]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[106]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[107]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[108]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[109]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[110]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[111]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[112]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[113]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[114]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[115]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[116]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[117]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[118]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[119]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[120]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[121]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[122]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[123]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[124]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[125]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[126]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).ascii[127]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[128 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[129 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[130 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[131 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[132 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[133 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[134 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[135 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[136 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[137 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[138 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[139 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[140 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[141 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[142 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[143 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[144 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[145 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[146 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[147 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[148 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[149 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[150 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[151 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[152 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[153 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[154 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[155 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[156 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[157 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[158 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[159 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[160 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[161 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[162 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[163 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[164 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[165 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[166 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[167 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[168 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[169 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[170 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[171 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[172 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[173 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[174 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[175 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[176 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[177 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[178 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[179 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[180 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[181 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[182 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[183 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[184 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[185 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[186 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[187 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[188 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[189 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[190 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[191 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[192 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[193 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[194 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[195 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[196 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[197 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[198 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[199 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[200 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[201 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[202 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[203 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[204 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[205 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[206 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[207 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[208 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[209 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[210 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[211 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[212 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[213 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[214 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[215 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[216 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[217 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[218 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[219 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[220 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[221 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[222 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[223 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[224 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[225 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[226 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[227 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[228 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[229 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[230 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[231 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[232 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[233 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[234 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[235 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[236 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[237 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[238 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[239 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[240 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[241 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[242 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[243 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[244 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[245 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[246 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[247 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[248 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[249 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[250 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[251 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[252 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[253 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[254 - 128]);
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(strings).latin1[255 - 128]);
    /* non-generated */
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(bytes_empty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(tuple_empty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(hamt_bitmap_node_empty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_INTERP_SINGLETON(interp, hamt_empty));
    _PyStaticObject_CheckRefcnt((PyObject *)&_Py_SINGLETON(context_token_missing));
}
#endif  // Py_DEBUG
/* End auto-generated code */

#ifdef __cplusplus
}
#endif
#endif /* !Py_INTERNAL_GLOBAL_OBJECTS_FINI_GENERATED_INIT_H */
