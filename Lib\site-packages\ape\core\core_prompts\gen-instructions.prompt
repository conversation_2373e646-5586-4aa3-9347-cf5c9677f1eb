---
model: gpt-4o
temperature: 0.7
inputs: 
    response_format_instructions: A description of the response format instructions
    task_description: A description of the task that the LLM will be asked to perform
    dataset_desc: A description of the dataset we are using
    task_fewshot: A list of few shot examples of the task
    basic_prompt: A basic prompt that we've used before
    tip: A suggestion for how to go with writing the new prompt
response_format:
    type: json_schema
    json_schema:
        name: prompt
        description: Creates a prompt consisting of a list of messages.
        strict: true
        schema:
            type: object
            required:
                - messages
            properties:
                messages:
                    type: array
                    description: List of messages in the prompt
                    items:
                        type: object
                        properties:
                            role:
                                type: string
                                description: Role of the message sender
                                enum:
                                    - system
                                    - user
                            content:
                                type: string
                                description: Content of the message
                        required:
                            - role
                            - content
                        additionalProperties: false
            additionalProperties: false
---
<system>
You are an expert prompt engineer.
As an expert prompt engineer, you might include few shot examples, chain of thought, etc. whichever way you like.

A good way to structure the prompt is to add format/persona/instructions to the system prompt, and user inputs to the user prompt.

{response_format_instructions}
</system>
<user>
Use the information below to learn about a task that we are trying to solve using calls to an LM, then write a new prompt that will be used to prompt a Language Model to better solve the task.

Task for LLM:
{prompt_desc}

Expected input variables:
{inputs_desc}

Expected output variables:
{outputs_desc}

Dataset summary (description of the dataset we are using):
{dataset_desc}

Task demos (example inputs/outputs of the task):
{task_fewshot}

Basic prompt:
{basic_prompt}

Idea:
{tip}
--
Suggestion for improvement (a suggestion for how to go with writing the new prompt):
{task_description}
</user>