---
model: gpt-4o-mini
inputs:
    question: str
    answer: str
    sentences: List[str]
response_format:
    type: json_schema
    json_schema:
        name: AnalysisResponse
        schema:
            type: object
            properties:
                analysis:
                    type: array
                    items:
                        type: object
                        properties:
                            sentence_index:
                                type: integer
                            simpler_statements:
                                type: array
                                items:
                                    type: string
                                additionalProperties: false
                        required:
                            - sentence_index
                            - simpler_statements
                        additionalProperties: false
            required:
                - analysis
            additionalProperties: false
---

<system>
Given a question, an answer, and sentences from the answer analyze the complexity of each sentence given under 'sentences' and break down each sentence into one or more fully understandable statements while also ensuring no pronouns are used in each statement. Format the outputs in JSON.

The output should be a well-formatted JSON instance.

Examples:

question: "Who was <PERSON> and what is he best known for?"
answer: "He was a German-born theoretical physicist, widely acknowledged to be one of the greatest and most influential physicists of all time. He was best known for developing the theory of relativity, he also made important contributions to the development of the theory of quantum mechanics."
sentences: "\n        0:He was a German-born theoretical physicist, widely acknowledged to be one of the greatest and most influential physicists of all time. \n        1:He was best known for developing the theory of relativity, he also made important contributions to the development of the theory of quantum mechanics.\n        "
analysis: {{"analysis":[{{"sentence_index": 0, "simpler_statements": ["<PERSON> was a German-born theoretical physicist.", "<PERSON> is recognized as one of the greatest and most influential physicists of all time."]}}, {{"sentence_index": 1, "simpler_statements": ["Albert Einstein was best known for developing the theory of relativity.", "<PERSON> Einstein also made important contributions to the development of the theory of quantum mechanics."]}}]}}

Your actual task:

question: {question}
answer: {answer}
sentences: {sentences}
analysis:
</system>
