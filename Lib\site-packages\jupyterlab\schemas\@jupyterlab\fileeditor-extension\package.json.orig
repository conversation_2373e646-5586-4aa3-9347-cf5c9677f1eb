{"name": "@jupyterlab/fileeditor-extension", "version": "4.3.6", "description": "<PERSON><PERSON><PERSON><PERSON>ab - Editor Widget Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@codemirror/commands": "^6.5.0", "@codemirror/search": "^6.5.6", "@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/codeeditor": "^4.3.6", "@jupyterlab/codemirror": "^4.3.6", "@jupyterlab/completer": "^4.3.6", "@jupyterlab/console": "^4.3.6", "@jupyterlab/coreutils": "^6.3.6", "@jupyterlab/docregistry": "^4.3.6", "@jupyterlab/documentsearch": "^4.3.6", "@jupyterlab/filebrowser": "^4.3.6", "@jupyterlab/fileeditor": "^4.3.6", "@jupyterlab/launcher": "^4.3.6", "@jupyterlab/lsp": "^4.3.6", "@jupyterlab/mainmenu": "^4.3.6", "@jupyterlab/observables": "^5.3.6", "@jupyterlab/rendermime-interfaces": "^3.11.6", "@jupyterlab/services": "^7.3.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/statusbar": "^4.3.6", "@jupyterlab/toc": "^6.3.6", "@jupyterlab/translation": "^4.3.6", "@jupyterlab/ui-components": "^4.3.6", "@lumino/algorithm": "^2.0.2", "@lumino/commands": "^2.3.1", "@lumino/coreutils": "^2.2.0", "@lumino/widgets": "^2.5.0"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}