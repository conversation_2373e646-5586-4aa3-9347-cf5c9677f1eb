"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[3330],{43330:(t,e,n)=>{n.d(e,{diagram:()=>Ut});var i=n(76235);var a=n(1056);var s=n(40055);var r=n(16750);var l=n(74353);var o=n.n(l);var h=n(42838);var d=n.n(h);var u=function(){var t=function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n},e=[1,24],n=[1,25],i=[1,26],a=[1,27],s=[1,28],r=[1,63],l=[1,64],o=[1,65],h=[1,66],d=[1,67],u=[1,68],p=[1,69],f=[1,29],y=[1,30],b=[1,31],g=[1,32],x=[1,33],_=[1,34],m=[1,35],E=[1,36],v=[1,37],A=[1,38],S=[1,39],C=[1,40],k=[1,41],O=[1,42],w=[1,43],T=[1,44],R=[1,45],D=[1,46],N=[1,47],P=[1,48],j=[1,50],M=[1,51],B=[1,52],L=[1,53],Y=[1,54],I=[1,55],U=[1,56],F=[1,57],X=[1,58],z=[1,59],W=[1,60],Q=[14,42],$=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],q=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],V=[1,82],H=[1,83],G=[1,84],K=[1,85],J=[12,14,42],Z=[12,14,33,42],tt=[12,14,33,42,76,77,79,80],et=[12,33],nt=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74];var it={trace:function t(){},yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:function t(e,n,i,a,s,r,l){var o=r.length-1;switch(s){case 3:a.setDirection("TB");break;case 4:a.setDirection("BT");break;case 5:a.setDirection("RL");break;case 6:a.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:a.setC4Type(r[o-3]);break;case 19:a.setTitle(r[o].substring(6));this.$=r[o].substring(6);break;case 20:a.setAccDescription(r[o].substring(15));this.$=r[o].substring(15);break;case 21:this.$=r[o].trim();a.setTitle(this.$);break;case 22:case 23:this.$=r[o].trim();a.setAccDescription(this.$);break;case 28:case 29:r[o].splice(2,0,"ENTERPRISE");a.addPersonOrSystemBoundary(...r[o]);this.$=r[o];break;case 30:a.addPersonOrSystemBoundary(...r[o]);this.$=r[o];break;case 31:r[o].splice(2,0,"CONTAINER");a.addContainerBoundary(...r[o]);this.$=r[o];break;case 32:a.addDeploymentNode("node",...r[o]);this.$=r[o];break;case 33:a.addDeploymentNode("nodeL",...r[o]);this.$=r[o];break;case 34:a.addDeploymentNode("nodeR",...r[o]);this.$=r[o];break;case 35:a.popBoundaryParseStack();break;case 39:a.addPersonOrSystem("person",...r[o]);this.$=r[o];break;case 40:a.addPersonOrSystem("external_person",...r[o]);this.$=r[o];break;case 41:a.addPersonOrSystem("system",...r[o]);this.$=r[o];break;case 42:a.addPersonOrSystem("system_db",...r[o]);this.$=r[o];break;case 43:a.addPersonOrSystem("system_queue",...r[o]);this.$=r[o];break;case 44:a.addPersonOrSystem("external_system",...r[o]);this.$=r[o];break;case 45:a.addPersonOrSystem("external_system_db",...r[o]);this.$=r[o];break;case 46:a.addPersonOrSystem("external_system_queue",...r[o]);this.$=r[o];break;case 47:a.addContainer("container",...r[o]);this.$=r[o];break;case 48:a.addContainer("container_db",...r[o]);this.$=r[o];break;case 49:a.addContainer("container_queue",...r[o]);this.$=r[o];break;case 50:a.addContainer("external_container",...r[o]);this.$=r[o];break;case 51:a.addContainer("external_container_db",...r[o]);this.$=r[o];break;case 52:a.addContainer("external_container_queue",...r[o]);this.$=r[o];break;case 53:a.addComponent("component",...r[o]);this.$=r[o];break;case 54:a.addComponent("component_db",...r[o]);this.$=r[o];break;case 55:a.addComponent("component_queue",...r[o]);this.$=r[o];break;case 56:a.addComponent("external_component",...r[o]);this.$=r[o];break;case 57:a.addComponent("external_component_db",...r[o]);this.$=r[o];break;case 58:a.addComponent("external_component_queue",...r[o]);this.$=r[o];break;case 60:a.addRel("rel",...r[o]);this.$=r[o];break;case 61:a.addRel("birel",...r[o]);this.$=r[o];break;case 62:a.addRel("rel_u",...r[o]);this.$=r[o];break;case 63:a.addRel("rel_d",...r[o]);this.$=r[o];break;case 64:a.addRel("rel_l",...r[o]);this.$=r[o];break;case 65:a.addRel("rel_r",...r[o]);this.$=r[o];break;case 66:a.addRel("rel_b",...r[o]);this.$=r[o];break;case 67:r[o].splice(0,1);a.addRel("rel",...r[o]);this.$=r[o];break;case 68:a.updateElStyle("update_el_style",...r[o]);this.$=r[o];break;case 69:a.updateRelStyle("update_rel_style",...r[o]);this.$=r[o];break;case 70:a.updateLayoutConfig("update_layout_config",...r[o]);this.$=r[o];break;case 71:this.$=[r[o]];break;case 72:r[o].unshift(r[o-1]);this.$=r[o];break;case 73:case 75:this.$=r[o].trim();break;case 74:let t={};t[r[o-1].trim()]=r[o].trim();this.$=t;break;case 76:this.$="";break}},table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:e,23:n,24:i,26:a,28:s,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{13:70,19:20,20:21,21:22,22:e,23:n,24:i,26:a,28:s,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{13:71,19:20,20:21,21:22,22:e,23:n,24:i,26:a,28:s,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{13:72,19:20,20:21,21:22,22:e,23:n,24:i,26:a,28:s,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{13:73,19:20,20:21,21:22,22:e,23:n,24:i,26:a,28:s,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{14:[1,74]},t(Q,[2,13],{43:23,29:49,30:61,32:62,20:75,34:r,36:l,37:o,38:h,39:d,40:u,41:p,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W}),t(Q,[2,14]),t($,[2,16],{12:[1,76]}),t(Q,[2,36],{12:[1,77]}),t(q,[2,19]),t(q,[2,20]),{25:[1,78]},{27:[1,79]},t(q,[2,23]),{35:80,75:81,76:V,77:H,79:G,80:K},{35:86,75:81,76:V,77:H,79:G,80:K},{35:87,75:81,76:V,77:H,79:G,80:K},{35:88,75:81,76:V,77:H,79:G,80:K},{35:89,75:81,76:V,77:H,79:G,80:K},{35:90,75:81,76:V,77:H,79:G,80:K},{35:91,75:81,76:V,77:H,79:G,80:K},{35:92,75:81,76:V,77:H,79:G,80:K},{35:93,75:81,76:V,77:H,79:G,80:K},{35:94,75:81,76:V,77:H,79:G,80:K},{35:95,75:81,76:V,77:H,79:G,80:K},{35:96,75:81,76:V,77:H,79:G,80:K},{35:97,75:81,76:V,77:H,79:G,80:K},{35:98,75:81,76:V,77:H,79:G,80:K},{35:99,75:81,76:V,77:H,79:G,80:K},{35:100,75:81,76:V,77:H,79:G,80:K},{35:101,75:81,76:V,77:H,79:G,80:K},{35:102,75:81,76:V,77:H,79:G,80:K},{35:103,75:81,76:V,77:H,79:G,80:K},{35:104,75:81,76:V,77:H,79:G,80:K},t(J,[2,59]),{35:105,75:81,76:V,77:H,79:G,80:K},{35:106,75:81,76:V,77:H,79:G,80:K},{35:107,75:81,76:V,77:H,79:G,80:K},{35:108,75:81,76:V,77:H,79:G,80:K},{35:109,75:81,76:V,77:H,79:G,80:K},{35:110,75:81,76:V,77:H,79:G,80:K},{35:111,75:81,76:V,77:H,79:G,80:K},{35:112,75:81,76:V,77:H,79:G,80:K},{35:113,75:81,76:V,77:H,79:G,80:K},{35:114,75:81,76:V,77:H,79:G,80:K},{35:115,75:81,76:V,77:H,79:G,80:K},{20:116,29:49,30:61,32:62,34:r,36:l,37:o,38:h,39:d,40:u,41:p,43:23,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W},{12:[1,118],33:[1,117]},{35:119,75:81,76:V,77:H,79:G,80:K},{35:120,75:81,76:V,77:H,79:G,80:K},{35:121,75:81,76:V,77:H,79:G,80:K},{35:122,75:81,76:V,77:H,79:G,80:K},{35:123,75:81,76:V,77:H,79:G,80:K},{35:124,75:81,76:V,77:H,79:G,80:K},{35:125,75:81,76:V,77:H,79:G,80:K},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},t(Q,[2,15]),t($,[2,17],{21:22,19:130,22:e,23:n,24:i,26:a,28:s}),t(Q,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:e,23:n,24:i,26:a,28:s,34:r,36:l,37:o,38:h,39:d,40:u,41:p,44:f,45:y,46:b,47:g,48:x,49:_,50:m,51:E,52:v,53:A,54:S,55:C,56:k,57:O,58:w,59:T,60:R,61:D,62:N,63:P,64:j,65:M,66:B,67:L,68:Y,69:I,70:U,71:F,72:X,73:z,74:W}),t(q,[2,21]),t(q,[2,22]),t(J,[2,39]),t(Z,[2,71],{75:81,35:132,76:V,77:H,79:G,80:K}),t(tt,[2,73]),{78:[1,133]},t(tt,[2,75]),t(tt,[2,76]),t(J,[2,40]),t(J,[2,41]),t(J,[2,42]),t(J,[2,43]),t(J,[2,44]),t(J,[2,45]),t(J,[2,46]),t(J,[2,47]),t(J,[2,48]),t(J,[2,49]),t(J,[2,50]),t(J,[2,51]),t(J,[2,52]),t(J,[2,53]),t(J,[2,54]),t(J,[2,55]),t(J,[2,56]),t(J,[2,57]),t(J,[2,58]),t(J,[2,60]),t(J,[2,61]),t(J,[2,62]),t(J,[2,63]),t(J,[2,64]),t(J,[2,65]),t(J,[2,66]),t(J,[2,67]),t(J,[2,68]),t(J,[2,69]),t(J,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},t(et,[2,28]),t(et,[2,29]),t(et,[2,30]),t(et,[2,31]),t(et,[2,32]),t(et,[2,33]),t(et,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},t($,[2,18]),t(Q,[2,38]),t(Z,[2,72]),t(tt,[2,74]),t(J,[2,24]),t(J,[2,35]),t(nt,[2,25]),t(nt,[2,26],{12:[1,138]}),t(nt,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:function t(e,n){if(n.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=n;throw i}},parse:function t(e){var n=this,i=[0],a=[],s=[null],r=[],l=this.table,o="",c=0,h=0,d=2,u=1;var p=r.slice.call(arguments,1);var f=Object.create(this.lexer);var y={yy:{}};for(var b in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,b)){y.yy[b]=this.yy[b]}}f.setInput(e,y.yy);y.yy.lexer=f;y.yy.parser=this;if(typeof f.yylloc=="undefined"){f.yylloc={}}var g=f.yylloc;r.push(g);var x=f.options&&f.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function _(){var t;t=a.pop()||f.lex()||u;if(typeof t!=="number"){if(t instanceof Array){a=t;t=a.pop()}t=n.symbols_[t]||t}return t}var m,E,v,A,S={},C,k,O,w;while(true){E=i[i.length-1];if(this.defaultActions[E]){v=this.defaultActions[E]}else{if(m===null||typeof m=="undefined"){m=_()}v=l[E]&&l[E][m]}if(typeof v==="undefined"||!v.length||!v[0]){var T="";w=[];for(C in l[E]){if(this.terminals_[C]&&C>d){w.push("'"+this.terminals_[C]+"'")}}if(f.showPosition){T="Parse error on line "+(c+1)+":\n"+f.showPosition()+"\nExpecting "+w.join(", ")+", got '"+(this.terminals_[m]||m)+"'"}else{T="Parse error on line "+(c+1)+": Unexpected "+(m==u?"end of input":"'"+(this.terminals_[m]||m)+"'")}this.parseError(T,{text:f.match,token:this.terminals_[m]||m,line:f.yylineno,loc:g,expected:w})}if(v[0]instanceof Array&&v.length>1){throw new Error("Parse Error: multiple actions possible at state: "+E+", token: "+m)}switch(v[0]){case 1:i.push(m);s.push(f.yytext);r.push(f.yylloc);i.push(v[1]);m=null;{h=f.yyleng;o=f.yytext;c=f.yylineno;g=f.yylloc}break;case 2:k=this.productions_[v[1]][1];S.$=s[s.length-k];S._$={first_line:r[r.length-(k||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(k||1)].first_column,last_column:r[r.length-1].last_column};if(x){S._$.range=[r[r.length-(k||1)].range[0],r[r.length-1].range[1]]}A=this.performAction.apply(S,[o,h,c,y.yy,v[1],s,r].concat(p));if(typeof A!=="undefined"){return A}if(k){i=i.slice(0,-1*k*2);s=s.slice(0,-1*k);r=r.slice(0,-1*k)}i.push(this.productions_[v[1]][0]);s.push(S.$);r.push(S._$);O=l[i[i.length-2]][i[i.length-1]];i.push(O);break;case 3:return true}}return true}};var at=function(){var t={EOF:1,parseError:function t(e,n){if(this.yy.parser){this.yy.parser.parseError(e,n)}else{throw new Error(e)}},setInput:function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var e=t.length;var n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(n.length-1){this.yylineno-=n.length-1}var a=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[a[0],a[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var n,i,a;if(this.options.backtrack_lexer){a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){a.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(n){return n}else if(this._backtrack){for(var s in a){this[s]=a[s]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,n,i;if(!this._more){this.yytext="";this.match=""}var a=this._currentRules();for(var s=0;s<a.length;s++){n=this._input.match(this.rules[a[s]]);if(n&&(!e||n[0].length>e[0].length)){e=n;i=s;if(this.options.backtrack_lexer){t=this.test_match(n,a[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,a[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var e=this.next();if(e){return e}else{return this.lex()}},begin:function t(e){this.conditionStack.push(e)},popState:function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}},pushState:function t(e){this.begin(e)},stateStackSize:function t(){return this.conditionStack.length},options:{},performAction:function t(e,n,i,a){switch(i){case 0:return 6;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 22;case 5:return 23;case 6:this.begin("acc_title");return 24;case 7:this.popState();return"acc_title_value";case 8:this.begin("acc_descr");return 26;case 9:this.popState();return"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:break;case 14:c;break;case 15:return 12;case 16:break;case 17:return 11;case 18:return 15;case 19:return 16;case 20:return 17;case 21:return 18;case 22:this.begin("person_ext");return 45;case 23:this.begin("person");return 44;case 24:this.begin("system_ext_queue");return 51;case 25:this.begin("system_ext_db");return 50;case 26:this.begin("system_ext");return 49;case 27:this.begin("system_queue");return 48;case 28:this.begin("system_db");return 47;case 29:this.begin("system");return 46;case 30:this.begin("boundary");return 37;case 31:this.begin("enterprise_boundary");return 34;case 32:this.begin("system_boundary");return 36;case 33:this.begin("container_ext_queue");return 57;case 34:this.begin("container_ext_db");return 56;case 35:this.begin("container_ext");return 55;case 36:this.begin("container_queue");return 54;case 37:this.begin("container_db");return 53;case 38:this.begin("container");return 52;case 39:this.begin("container_boundary");return 38;case 40:this.begin("component_ext_queue");return 63;case 41:this.begin("component_ext_db");return 62;case 42:this.begin("component_ext");return 61;case 43:this.begin("component_queue");return 60;case 44:this.begin("component_db");return 59;case 45:this.begin("component");return 58;case 46:this.begin("node");return 39;case 47:this.begin("node");return 39;case 48:this.begin("node_l");return 40;case 49:this.begin("node_r");return 41;case 50:this.begin("rel");return 64;case 51:this.begin("birel");return 65;case 52:this.begin("rel_u");return 66;case 53:this.begin("rel_u");return 66;case 54:this.begin("rel_d");return 67;case 55:this.begin("rel_d");return 67;case 56:this.begin("rel_l");return 68;case 57:this.begin("rel_l");return 68;case 58:this.begin("rel_r");return 69;case 59:this.begin("rel_r");return 69;case 60:this.begin("rel_b");return 70;case 61:this.begin("rel_index");return 71;case 62:this.begin("update_el_style");return 72;case 63:this.begin("update_rel_style");return 73;case 64:this.begin("update_layout_config");return 74;case 65:return"EOF_IN_STRUCT";case 66:this.begin("attribute");return"ATTRIBUTE_EMPTY";case 67:this.begin("attribute");break;case 68:this.popState();this.popState();break;case 69:return 80;case 70:break;case 71:return 80;case 72:this.begin("string");break;case 73:this.popState();break;case 74:return"STR";case 75:this.begin("string_kv");break;case 76:this.begin("string_kv_key");return"STR_KEY";case 77:this.popState();this.begin("string_kv_value");break;case 78:return"STR_VALUE";case 79:this.popState();this.popState();break;case 80:return"STR";case 81:return"LBRACE";case 82:return"RBRACE";case 83:return"SPACE";case 84:return"EOL";case 85:return 14}},rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:false},acc_descr:{rules:[9],inclusive:false},acc_title:{rules:[7],inclusive:false},string_kv_value:{rules:[78,79],inclusive:false},string_kv_key:{rules:[77],inclusive:false},string_kv:{rules:[76],inclusive:false},string:{rules:[73,74],inclusive:false},attribute:{rules:[68,69,70,71,72,75,80],inclusive:false},update_layout_config:{rules:[65,66,67,68],inclusive:false},update_rel_style:{rules:[65,66,67,68],inclusive:false},update_el_style:{rules:[65,66,67,68],inclusive:false},rel_b:{rules:[65,66,67,68],inclusive:false},rel_r:{rules:[65,66,67,68],inclusive:false},rel_l:{rules:[65,66,67,68],inclusive:false},rel_d:{rules:[65,66,67,68],inclusive:false},rel_u:{rules:[65,66,67,68],inclusive:false},rel_bi:{rules:[],inclusive:false},rel:{rules:[65,66,67,68],inclusive:false},node_r:{rules:[65,66,67,68],inclusive:false},node_l:{rules:[65,66,67,68],inclusive:false},node:{rules:[65,66,67,68],inclusive:false},index:{rules:[],inclusive:false},rel_index:{rules:[65,66,67,68],inclusive:false},component_ext_queue:{rules:[],inclusive:false},component_ext_db:{rules:[65,66,67,68],inclusive:false},component_ext:{rules:[65,66,67,68],inclusive:false},component_queue:{rules:[65,66,67,68],inclusive:false},component_db:{rules:[65,66,67,68],inclusive:false},component:{rules:[65,66,67,68],inclusive:false},container_boundary:{rules:[65,66,67,68],inclusive:false},container_ext_queue:{rules:[65,66,67,68],inclusive:false},container_ext_db:{rules:[65,66,67,68],inclusive:false},container_ext:{rules:[65,66,67,68],inclusive:false},container_queue:{rules:[65,66,67,68],inclusive:false},container_db:{rules:[65,66,67,68],inclusive:false},container:{rules:[65,66,67,68],inclusive:false},birel:{rules:[65,66,67,68],inclusive:false},system_boundary:{rules:[65,66,67,68],inclusive:false},enterprise_boundary:{rules:[65,66,67,68],inclusive:false},boundary:{rules:[65,66,67,68],inclusive:false},system_ext_queue:{rules:[65,66,67,68],inclusive:false},system_ext_db:{rules:[65,66,67,68],inclusive:false},system_ext:{rules:[65,66,67,68],inclusive:false},system_queue:{rules:[65,66,67,68],inclusive:false},system_db:{rules:[65,66,67,68],inclusive:false},system:{rules:[65,66,67,68],inclusive:false},person_ext:{rules:[65,66,67,68],inclusive:false},person:{rules:[65,66,67,68],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:true}}};return t}();it.lexer=at;function st(){this.yy={}}st.prototype=it;it.Parser=st;return new st}();u.parser=u;const p=u;let f=[];let y=[""];let b="global";let g="";let x=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}];let _=[];let m="";let E=false;let v=4;let A=2;var S;const C=function(){return S};const k=function(t){let e=(0,i.d)(t,(0,i.c)());S=e};const O=function(t,e,n,i,a,s,r,l,o){if(t===void 0||t===null||e===void 0||e===null||n===void 0||n===null||i===void 0||i===null){return}let c={};const h=_.find((t=>t.from===e&&t.to===n));if(h){c=h}else{_.push(c)}c.type=t;c.from=e;c.to=n;c.label={text:i};if(a===void 0||a===null){c.techn={text:""}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];c[t]={text:e}}else{c.techn={text:a}}}if(s===void 0||s===null){c.descr={text:""}}else{if(typeof s==="object"){let[t,e]=Object.entries(s)[0];c[t]={text:e}}else{c.descr={text:s}}}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];c[t]=e}else{c.sprite=r}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];c[t]=e}else{c.tags=l}if(typeof o==="object"){let[t,e]=Object.entries(o)[0];c[t]=e}else{c.link=o}c.wrap=H()};const w=function(t,e,n,i,a,s,r){if(e===null||n===null){return}let l={};const o=f.find((t=>t.alias===e));if(o&&e===o.alias){l=o}else{l.alias=e;f.push(l)}if(n===void 0||n===null){l.label={text:""}}else{l.label={text:n}}if(i===void 0||i===null){l.descr={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];l[t]={text:e}}else{l.descr={text:i}}}if(typeof a==="object"){let[t,e]=Object.entries(a)[0];l[t]=e}else{l.sprite=a}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];l[t]=e}else{l.tags=s}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];l[t]=e}else{l.link=r}l.typeC4Shape={text:t};l.parentBoundary=b;l.wrap=H()};const T=function(t,e,n,i,a,s,r,l){if(e===null||n===null){return}let o={};const c=f.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;f.push(o)}if(n===void 0||n===null){o.label={text:""}}else{o.label={text:n}}if(i===void 0||i===null){o.techn={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.techn={text:i}}}if(a===void 0||a===null){o.descr={text:""}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else{o.descr={text:a}}}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];o[t]=e}else{o.sprite=s}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]=e}else{o.tags=r}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.wrap=H();o.typeC4Shape={text:t};o.parentBoundary=b};const R=function(t,e,n,i,a,s,r,l){if(e===null||n===null){return}let o={};const c=f.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;f.push(o)}if(n===void 0||n===null){o.label={text:""}}else{o.label={text:n}}if(i===void 0||i===null){o.techn={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.techn={text:i}}}if(a===void 0||a===null){o.descr={text:""}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else{o.descr={text:a}}}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];o[t]=e}else{o.sprite=s}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]=e}else{o.tags=r}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.wrap=H();o.typeC4Shape={text:t};o.parentBoundary=b};const D=function(t,e,n,i,a){if(t===null||e===null){return}let s={};const r=x.find((e=>e.alias===t));if(r&&t===r.alias){s=r}else{s.alias=t;x.push(s)}if(e===void 0||e===null){s.label={text:""}}else{s.label={text:e}}if(n===void 0||n===null){s.type={text:"system"}}else{if(typeof n==="object"){let[t,e]=Object.entries(n)[0];s[t]={text:e}}else{s.type={text:n}}}if(typeof i==="object"){let[t,e]=Object.entries(i)[0];s[t]=e}else{s.tags=i}if(typeof a==="object"){let[t,e]=Object.entries(a)[0];s[t]=e}else{s.link=a}s.parentBoundary=b;s.wrap=H();g=b;b=t;y.push(g)};const N=function(t,e,n,i,a){if(t===null||e===null){return}let s={};const r=x.find((e=>e.alias===t));if(r&&t===r.alias){s=r}else{s.alias=t;x.push(s)}if(e===void 0||e===null){s.label={text:""}}else{s.label={text:e}}if(n===void 0||n===null){s.type={text:"container"}}else{if(typeof n==="object"){let[t,e]=Object.entries(n)[0];s[t]={text:e}}else{s.type={text:n}}}if(typeof i==="object"){let[t,e]=Object.entries(i)[0];s[t]=e}else{s.tags=i}if(typeof a==="object"){let[t,e]=Object.entries(a)[0];s[t]=e}else{s.link=a}s.parentBoundary=b;s.wrap=H();g=b;b=t;y.push(g)};const P=function(t,e,n,i,a,s,r,l){if(e===null||n===null){return}let o={};const c=x.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;x.push(o)}if(n===void 0||n===null){o.label={text:""}}else{o.label={text:n}}if(i===void 0||i===null){o.type={text:"node"}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.type={text:i}}}if(a===void 0||a===null){o.descr={text:""}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];o[t]={text:e}}else{o.descr={text:a}}}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]=e}else{o.tags=r}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.nodeType=t;o.parentBoundary=b;o.wrap=H();g=b;b=e;y.push(g)};const j=function(){b=g;y.pop();g=y.pop();y.push(g)};const M=function(t,e,n,i,a,s,r,l,o,c,h){let d=f.find((t=>t.alias===e));if(d===void 0){d=x.find((t=>t.alias===e));if(d===void 0){return}}if(n!==void 0&&n!==null){if(typeof n==="object"){let[t,e]=Object.entries(n)[0];d[t]=e}else{d.bgColor=n}}if(i!==void 0&&i!==null){if(typeof i==="object"){let[t,e]=Object.entries(i)[0];d[t]=e}else{d.fontColor=i}}if(a!==void 0&&a!==null){if(typeof a==="object"){let[t,e]=Object.entries(a)[0];d[t]=e}else{d.borderColor=a}}if(s!==void 0&&s!==null){if(typeof s==="object"){let[t,e]=Object.entries(s)[0];d[t]=e}else{d.shadowing=s}}if(r!==void 0&&r!==null){if(typeof r==="object"){let[t,e]=Object.entries(r)[0];d[t]=e}else{d.shape=r}}if(l!==void 0&&l!==null){if(typeof l==="object"){let[t,e]=Object.entries(l)[0];d[t]=e}else{d.sprite=l}}if(o!==void 0&&o!==null){if(typeof o==="object"){let[t,e]=Object.entries(o)[0];d[t]=e}else{d.techn=o}}if(c!==void 0&&c!==null){if(typeof c==="object"){let[t,e]=Object.entries(c)[0];d[t]=e}else{d.legendText=c}}if(h!==void 0&&h!==null){if(typeof h==="object"){let[t,e]=Object.entries(h)[0];d[t]=e}else{d.legendSprite=h}}};const B=function(t,e,n,i,a,s,r){const l=_.find((t=>t.from===e&&t.to===n));if(l===void 0){return}if(i!==void 0&&i!==null){if(typeof i==="object"){let[t,e]=Object.entries(i)[0];l[t]=e}else{l.textColor=i}}if(a!==void 0&&a!==null){if(typeof a==="object"){let[t,e]=Object.entries(a)[0];l[t]=e}else{l.lineColor=a}}if(s!==void 0&&s!==null){if(typeof s==="object"){let[t,e]=Object.entries(s)[0];l[t]=parseInt(e)}else{l.offsetX=parseInt(s)}}if(r!==void 0&&r!==null){if(typeof r==="object"){let[t,e]=Object.entries(r)[0];l[t]=parseInt(e)}else{l.offsetY=parseInt(r)}}};const L=function(t,e,n){let i=v;let a=A;if(typeof e==="object"){const t=Object.values(e)[0];i=parseInt(t)}else{i=parseInt(e)}if(typeof n==="object"){const t=Object.values(n)[0];a=parseInt(t)}else{a=parseInt(n)}if(i>=1){v=i}if(a>=1){A=a}};const Y=function(){return v};const I=function(){return A};const U=function(){return b};const F=function(){return g};const X=function(t){if(t===void 0||t===null){return f}else{return f.filter((e=>e.parentBoundary===t))}};const z=function(t){return f.find((e=>e.alias===t))};const W=function(t){return Object.keys(X(t))};const Q=function(t){if(t===void 0||t===null){return x}else{return x.filter((e=>e.parentBoundary===t))}};const $=function(){return _};const q=function(){return m};const V=function(t){E=t};const H=function(){return E};const G=function(){f=[];x=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}];g="";b="global";y=[""];_=[];y=[""];m="";E=false;v=4;A=2};const K={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25};const J={FILLED:0,OPEN:1};const Z={LEFTOF:0,RIGHTOF:1,OVER:2};const tt=function(t){let e=(0,i.d)(t,(0,i.c)());m=e};const et={addPersonOrSystem:w,addPersonOrSystemBoundary:D,addContainer:T,addContainerBoundary:N,addComponent:R,addDeploymentNode:P,popBoundaryParseStack:j,addRel:O,updateElStyle:M,updateRelStyle:B,updateLayoutConfig:L,autoWrap:H,setWrap:V,getC4ShapeArray:X,getC4Shape:z,getC4ShapeKeys:W,getBoundarys:Q,getCurrentBoundaryParse:U,getParentBoundaryParse:F,getRels:$,getTitle:q,getC4Type:C,getC4ShapeInRow:Y,getC4BoundaryInRow:I,setAccTitle:i.s,getAccTitle:i.g,getAccDescription:i.a,setAccDescription:i.b,getConfig:()=>(0,i.c)().c4,clear:G,LINETYPE:K,ARROWTYPE:J,PLACEMENT:Z,setTitle:tt,setC4Type:k};const nt=function(t,e){return(0,s.d)(t,e)};const it=function(t,e,n,i,a,s){const l=t.append("image");l.attr("width",e);l.attr("height",n);l.attr("x",i);l.attr("y",a);let o=s.startsWith("data:image/png;base64")?s:(0,r.Jf)(s);l.attr("xlink:href",o)};const at=(t,e,n)=>{const i=t.append("g");let a=0;for(let s of e){let t=s.textColor?s.textColor:"#444444";let e=s.lineColor?s.lineColor:"#444444";let r=s.offsetX?parseInt(s.offsetX):0;let l=s.offsetY?parseInt(s.offsetY):0;let o="";if(a===0){let t=i.append("line");t.attr("x1",s.startPoint.x);t.attr("y1",s.startPoint.y);t.attr("x2",s.endPoint.x);t.attr("y2",s.endPoint.y);t.attr("stroke-width","1");t.attr("stroke",e);t.style("fill","none");if(s.type!=="rel_b"){t.attr("marker-end","url("+o+"#arrowhead)")}if(s.type==="birel"||s.type==="rel_b"){t.attr("marker-start","url("+o+"#arrowend)")}a=-1}else{let t=i.append("path");t.attr("fill","none").attr("stroke-width","1").attr("stroke",e).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",s.startPoint.x).replaceAll("starty",s.startPoint.y).replaceAll("controlx",s.startPoint.x+(s.endPoint.x-s.startPoint.x)/2-(s.endPoint.x-s.startPoint.x)/4).replaceAll("controly",s.startPoint.y+(s.endPoint.y-s.startPoint.y)/2).replaceAll("stopx",s.endPoint.x).replaceAll("stopy",s.endPoint.y));if(s.type!=="rel_b"){t.attr("marker-end","url("+o+"#arrowhead)")}if(s.type==="birel"||s.type==="rel_b"){t.attr("marker-start","url("+o+"#arrowend)")}}let c=n.messageFont();bt(n)(s.label.text,i,Math.min(s.startPoint.x,s.endPoint.x)+Math.abs(s.endPoint.x-s.startPoint.x)/2+r,Math.min(s.startPoint.y,s.endPoint.y)+Math.abs(s.endPoint.y-s.startPoint.y)/2+l,s.label.width,s.label.height,{fill:t},c);if(s.techn&&s.techn.text!==""){c=n.messageFont();bt(n)("["+s.techn.text+"]",i,Math.min(s.startPoint.x,s.endPoint.x)+Math.abs(s.endPoint.x-s.startPoint.x)/2+r,Math.min(s.startPoint.y,s.endPoint.y)+Math.abs(s.endPoint.y-s.startPoint.y)/2+n.messageFontSize+5+l,Math.max(s.label.width,s.techn.width),s.techn.height,{fill:t,"font-style":"italic"},c)}}};const st=function(t,e,n){const i=t.append("g");let a=e.bgColor?e.bgColor:"none";let s=e.borderColor?e.borderColor:"#444444";let r=e.fontColor?e.fontColor:"black";let l={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};if(e.nodeType){l={"stroke-width":1}}let o={x:e.x,y:e.y,fill:a,stroke:s,width:e.width,height:e.height,rx:2.5,ry:2.5,attrs:l};nt(i,o);let c=n.boundaryFont();c.fontWeight="bold";c.fontSize=c.fontSize+2;c.fontColor=r;bt(n)(e.label.text,i,e.x,e.y+e.label.Y,e.width,e.height,{fill:"#444444"},c);if(e.type&&e.type.text!==""){c=n.boundaryFont();c.fontColor=r;bt(n)(e.type.text,i,e.x,e.y+e.type.Y,e.width,e.height,{fill:"#444444"},c)}if(e.descr&&e.descr.text!==""){c=n.boundaryFont();c.fontSize=c.fontSize-2;c.fontColor=r;bt(n)(e.descr.text,i,e.x,e.y+e.descr.Y,e.width,e.height,{fill:"#444444"},c)}};const rt=function(t,e,n){var i;let a=e.bgColor?e.bgColor:n[e.typeC4Shape.text+"_bg_color"];let r=e.borderColor?e.borderColor:n[e.typeC4Shape.text+"_border_color"];let l=e.fontColor?e.fontColor:"#FFFFFF";let o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(e.typeC4Shape.text){case"person":o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=";break}const c=t.append("g");c.attr("class","person-man");const h=(0,s.g)();switch(e.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":h.x=e.x;h.y=e.y;h.fill=a;h.width=e.width;h.height=e.height;h.stroke=r;h.rx=2.5;h.ry=2.5;h.attrs={"stroke-width":.5};nt(c,h);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":c.append("path").attr("fill",a).attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2).replaceAll("height",e.height));c.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":c.append("path").attr("fill",a).attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("width",e.width).replaceAll("half",e.height/2));c.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",e.x+e.width).replaceAll("starty",e.y).replaceAll("half",e.height/2));break}let d=yt(n,e.typeC4Shape.text);c.append("text").attr("fill",l).attr("font-family",d.fontFamily).attr("font-size",d.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",e.typeC4Shape.width).attr("x",e.x+e.width/2-e.typeC4Shape.width/2).attr("y",e.y+e.typeC4Shape.Y).text("<<"+e.typeC4Shape.text+">>");switch(e.typeC4Shape.text){case"person":case"external_person":it(c,48,48,e.x+e.width/2-24,e.y+e.image.Y,o);break}let u=n[e.typeC4Shape.text+"Font"]();u.fontWeight="bold";u.fontSize=u.fontSize+2;u.fontColor=l;bt(n)(e.label.text,c,e.x,e.y+e.label.Y,e.width,e.height,{fill:l},u);u=n[e.typeC4Shape.text+"Font"]();u.fontColor=l;if(e.techn&&((i=e.techn)==null?void 0:i.text)!==""){bt(n)(e.techn.text,c,e.x,e.y+e.techn.Y,e.width,e.height,{fill:l,"font-style":"italic"},u)}else if(e.type&&e.type.text!==""){bt(n)(e.type.text,c,e.x,e.y+e.type.Y,e.width,e.height,{fill:l,"font-style":"italic"},u)}if(e.descr&&e.descr.text!==""){u=n.personFont();u.fontColor=l;bt(n)(e.descr.text,c,e.x,e.y+e.descr.Y,e.width,e.height,{fill:l},u)}return e.height};const lt=function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")};const ot=function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")};const ct=function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")};const ht=function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")};const dt=function(t){t.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")};const ut=function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")};const pt=function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)};const ft=function(t){const e=t.append("defs");const n=e.append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);n.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z");n.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")};const yt=(t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]});const bt=function(){function t(t,e,n,i,s,r,l){const o=e.append("text").attr("x",n+s/2).attr("y",i+r/2+5).style("text-anchor","middle").text(t);a(o,l)}function e(t,e,n,s,r,l,o,c){const{fontSize:h,fontFamily:d,fontWeight:u}=c;const p=t.split(i.e.lineBreakRegex);for(let i=0;i<p.length;i++){const t=i*h-h*(p.length-1)/2;const l=e.append("text").attr("x",n+r/2).attr("y",s).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",h).style("font-weight",u).style("font-family",d);l.append("tspan").attr("dy",t).text(p[i]).attr("alignment-baseline","mathematical");a(l,o)}}function n(t,n,i,s,r,l,o,c){const h=n.append("switch");const d=h.append("foreignObject").attr("x",i).attr("y",s).attr("width",r).attr("height",l);const u=d.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");u.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,h,i,s,r,l,o,c);a(u,o)}function a(t,e){for(const n in e){if(e.hasOwnProperty(n)){t.attr(n,e[n])}}}return function(i){return i.textPlacement==="fo"?n:i.textPlacement==="old"?t:e}}();const gt={drawRect:nt,drawBoundary:st,drawC4Shape:rt,drawRels:at,drawImage:it,insertArrowHead:ht,insertArrowEnd:dt,insertArrowFilledHead:ut,insertDynamicNumber:pt,insertArrowCrossHead:ft,insertDatabaseIcon:lt,insertComputerIcon:ot,insertClockIcon:ct};let xt=0,_t=0;let mt=4;let Et=2;u.yy=et;let vt={};class At{constructor(t){this.name="";this.data={};this.data.startx=void 0;this.data.stopx=void 0;this.data.starty=void 0;this.data.stopy=void 0;this.data.widthLimit=void 0;this.nextData={};this.nextData.startx=void 0;this.nextData.stopx=void 0;this.nextData.starty=void 0;this.nextData.stopy=void 0;this.nextData.cnt=0;St(t.db.getConfig())}setData(t,e,n,i){this.nextData.startx=this.data.startx=t;this.nextData.stopx=this.data.stopx=e;this.nextData.starty=this.data.starty=n;this.nextData.stopy=this.data.stopy=i}updateVal(t,e,n,i){if(t[e]===void 0){t[e]=n}else{t[e]=i(n,t[e])}}insert(t){this.nextData.cnt=this.nextData.cnt+1;let e=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+t.margin*2;let n=e+t.width;let i=this.nextData.starty+t.margin*2;let a=i+t.height;if(e>=this.data.widthLimit||n>=this.data.widthLimit||this.nextData.cnt>mt){e=this.nextData.startx+t.margin+vt.nextLinePaddingX;i=this.nextData.stopy+t.margin*2;this.nextData.stopx=n=e+t.width;this.nextData.starty=this.nextData.stopy;this.nextData.stopy=a=i+t.height;this.nextData.cnt=1}t.x=e;t.y=i;this.updateVal(this.data,"startx",e,Math.min);this.updateVal(this.data,"starty",i,Math.min);this.updateVal(this.data,"stopx",n,Math.max);this.updateVal(this.data,"stopy",a,Math.max);this.updateVal(this.nextData,"startx",e,Math.min);this.updateVal(this.nextData,"starty",i,Math.min);this.updateVal(this.nextData,"stopx",n,Math.max);this.updateVal(this.nextData,"stopy",a,Math.max)}init(t){this.name="";this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0};this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0};St(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t;this.data.stopy+=t}}const St=function(t){(0,i.f)(vt,t);if(t.fontFamily){vt.personFontFamily=vt.systemFontFamily=vt.messageFontFamily=t.fontFamily}if(t.fontSize){vt.personFontSize=vt.systemFontSize=vt.messageFontSize=t.fontSize}if(t.fontWeight){vt.personFontWeight=vt.systemFontWeight=vt.messageFontWeight=t.fontWeight}};const Ct=(t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]});const kt=t=>({fontFamily:t.boundaryFontFamily,fontSize:t.boundaryFontSize,fontWeight:t.boundaryFontWeight});const Ot=t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight});function wt(t,e,n,a,s){if(!e[t].width){if(n){e[t].text=(0,i.w)(e[t].text,s,a);e[t].textLines=e[t].text.split(i.e.lineBreakRegex).length;e[t].width=s;e[t].height=(0,i.j)(e[t].text,a)}else{let n=e[t].text.split(i.e.lineBreakRegex);e[t].textLines=n.length;let s=0;e[t].height=0;e[t].width=0;for(const r of n){e[t].width=Math.max((0,i.h)(r,a),e[t].width);s=(0,i.j)(r,a);e[t].height=e[t].height+s}}}}const Tt=function(t,e,n){e.x=n.data.startx;e.y=n.data.starty;e.width=n.data.stopx-n.data.startx;e.height=n.data.stopy-n.data.starty;e.label.y=vt.c4ShapeMargin-35;let a=e.wrap&&vt.wrap;let s=kt(vt);s.fontSize=s.fontSize+2;s.fontWeight="bold";let r=(0,i.h)(e.label.text,s);wt("label",e,a,s,r);gt.drawBoundary(t,e,vt)};const Rt=function(t,e,n,a){let s=0;for(const r of a){s=0;const a=n[r];let l=Ct(vt,a.typeC4Shape.text);l.fontSize=l.fontSize-2;a.typeC4Shape.width=(0,i.h)("«"+a.typeC4Shape.text+"»",l);a.typeC4Shape.height=l.fontSize+2;a.typeC4Shape.Y=vt.c4ShapePadding;s=a.typeC4Shape.Y+a.typeC4Shape.height-4;a.image={width:0,height:0,Y:0};switch(a.typeC4Shape.text){case"person":case"external_person":a.image.width=48;a.image.height=48;a.image.Y=s;s=a.image.Y+a.image.height;break}if(a.sprite){a.image.width=48;a.image.height=48;a.image.Y=s;s=a.image.Y+a.image.height}let o=a.wrap&&vt.wrap;let c=vt.width-vt.c4ShapePadding*2;let h=Ct(vt,a.typeC4Shape.text);h.fontSize=h.fontSize+2;h.fontWeight="bold";wt("label",a,o,h,c);a["label"].Y=s+8;s=a["label"].Y+a["label"].height;if(a.type&&a.type.text!==""){a.type.text="["+a.type.text+"]";let t=Ct(vt,a.typeC4Shape.text);wt("type",a,o,t,c);a["type"].Y=s+5;s=a["type"].Y+a["type"].height}else if(a.techn&&a.techn.text!==""){a.techn.text="["+a.techn.text+"]";let t=Ct(vt,a.techn.text);wt("techn",a,o,t,c);a["techn"].Y=s+5;s=a["techn"].Y+a["techn"].height}let d=s;let u=a.label.width;if(a.descr&&a.descr.text!==""){let t=Ct(vt,a.typeC4Shape.text);wt("descr",a,o,t,c);a["descr"].Y=s+20;s=a["descr"].Y+a["descr"].height;u=Math.max(a.label.width,a.descr.width);d=s-a["descr"].textLines*5}u=u+vt.c4ShapePadding;a.width=Math.max(a.width||vt.width,u,vt.width);a.height=Math.max(a.height||vt.height,d,vt.height);a.margin=a.margin||vt.c4ShapeMargin;t.insert(a);gt.drawC4Shape(e,a,vt)}t.bumpLastMargin(vt.c4ShapeMargin)};class Dt{constructor(t,e){this.x=t;this.y=e}}let Nt=function(t,e){let n=t.x;let i=t.y;let a=e.x;let s=e.y;let r=n+t.width/2;let l=i+t.height/2;let o=Math.abs(n-a);let c=Math.abs(i-s);let h=c/o;let d=t.height/t.width;let u=null;if(i==s&&n<a){u=new Dt(n+t.width,l)}else if(i==s&&n>a){u=new Dt(n,l)}else if(n==a&&i<s){u=new Dt(r,i+t.height)}else if(n==a&&i>s){u=new Dt(r,i)}if(n>a&&i<s){if(d>=h){u=new Dt(n,l+h*t.width/2)}else{u=new Dt(r-o/c*t.height/2,i+t.height)}}else if(n<a&&i<s){if(d>=h){u=new Dt(n+t.width,l+h*t.width/2)}else{u=new Dt(r+o/c*t.height/2,i+t.height)}}else if(n<a&&i>s){if(d>=h){u=new Dt(n+t.width,l-h*t.width/2)}else{u=new Dt(r+t.height/2*o/c,i)}}else if(n>a&&i>s){if(d>=h){u=new Dt(n,l-t.width/2*h)}else{u=new Dt(r-t.height/2*o/c,i)}}return u};let Pt=function(t,e){let n={x:0,y:0};n.x=e.x+e.width/2;n.y=e.y+e.height/2;let i=Nt(t,n);n.x=t.x+t.width/2;n.y=t.y+t.height/2;let a=Nt(e,n);return{startPoint:i,endPoint:a}};const jt=function(t,e,n,a){let s=0;for(let r of e){s=s+1;let t=r.wrap&&vt.wrap;let e=Ot(vt);let l=a.db.getC4Type();if(l==="C4Dynamic"){r.label.text=s+": "+r.label.text}let o=(0,i.h)(r.label.text,e);wt("label",r,t,e,o);if(r.techn&&r.techn.text!==""){o=(0,i.h)(r.techn.text,e);wt("techn",r,t,e,o)}if(r.descr&&r.descr.text!==""){o=(0,i.h)(r.descr.text,e);wt("descr",r,t,e,o)}let c=n(r.from);let h=n(r.to);let d=Pt(c,h);r.startPoint=d.startPoint;r.endPoint=d.endPoint}gt.drawRels(t,e,vt)};function Mt(t,e,n,i,a){let s=new At(a);s.data.widthLimit=n.data.widthLimit/Math.min(Et,i.length);for(let[r,l]of i.entries()){let i=0;l.image={width:0,height:0,Y:0};if(l.sprite){l.image.width=48;l.image.height=48;l.image.Y=i;i=l.image.Y+l.image.height}let o=l.wrap&&vt.wrap;let c=kt(vt);c.fontSize=c.fontSize+2;c.fontWeight="bold";wt("label",l,o,c,s.data.widthLimit);l["label"].Y=i+8;i=l["label"].Y+l["label"].height;if(l.type&&l.type.text!==""){l.type.text="["+l.type.text+"]";let t=kt(vt);wt("type",l,o,t,s.data.widthLimit);l["type"].Y=i+5;i=l["type"].Y+l["type"].height}if(l.descr&&l.descr.text!==""){let t=kt(vt);t.fontSize=t.fontSize-2;wt("descr",l,o,t,s.data.widthLimit);l["descr"].Y=i+20;i=l["descr"].Y+l["descr"].height}if(r==0||r%Et===0){let t=n.data.startx+vt.diagramMarginX;let e=n.data.stopy+vt.diagramMarginY+i;s.setData(t,t,e,e)}else{let t=s.data.stopx!==s.data.startx?s.data.stopx+vt.diagramMarginX:s.data.startx;let e=s.data.starty;s.setData(t,t,e,e)}s.name=l.alias;let h=a.db.getC4ShapeArray(l.alias);let d=a.db.getC4ShapeKeys(l.alias);if(d.length>0){Rt(s,t,h,d)}e=l.alias;let u=a.db.getBoundarys(e);if(u.length>0){Mt(t,e,s,u,a)}if(l.alias!=="global"){Tt(t,l,s)}n.data.stopy=Math.max(s.data.stopy+vt.c4ShapeMargin,n.data.stopy);n.data.stopx=Math.max(s.data.stopx+vt.c4ShapeMargin,n.data.stopx);xt=Math.max(xt,n.data.stopx);_t=Math.max(_t,n.data.stopy)}}const Bt=function(t,e,n,s){vt=(0,i.c)().c4;const r=(0,i.c)().securityLevel;let l;if(r==="sandbox"){l=(0,a.Ltv)("#i"+e)}const o=r==="sandbox"?(0,a.Ltv)(l.nodes()[0].contentDocument.body):(0,a.Ltv)("body");let c=s.db;s.db.setWrap(vt.wrap);mt=c.getC4ShapeInRow();Et=c.getC4BoundaryInRow();i.l.debug(`C:${JSON.stringify(vt,null,2)}`);const h=r==="sandbox"?o.select(`[id="${e}"]`):(0,a.Ltv)(`[id="${e}"]`);gt.insertComputerIcon(h);gt.insertDatabaseIcon(h);gt.insertClockIcon(h);let d=new At(s);d.setData(vt.diagramMarginX,vt.diagramMarginX,vt.diagramMarginY,vt.diagramMarginY);d.data.widthLimit=screen.availWidth;xt=vt.diagramMarginX;_t=vt.diagramMarginY;const u=s.db.getTitle();let p=s.db.getBoundarys("");Mt(h,"",d,p,s);gt.insertArrowHead(h);gt.insertArrowEnd(h);gt.insertArrowCrossHead(h);gt.insertArrowFilledHead(h);jt(h,s.db.getRels(),s.db.getC4Shape,s);d.data.stopx=xt;d.data.stopy=_t;const f=d.data;let y=f.stopy-f.starty;let b=y+2*vt.diagramMarginY;let g=f.stopx-f.startx;const x=g+2*vt.diagramMarginX;if(u){h.append("text").text(u).attr("x",(f.stopx-f.startx)/2-4*vt.diagramMarginX).attr("y",f.starty+vt.diagramMarginY)}(0,i.i)(h,b,x,vt.useMaxWidth);const _=u?60:0;h.attr("viewBox",f.startx-vt.diagramMarginX+" -"+(vt.diagramMarginY+_)+" "+x+" "+(b+_));i.l.debug(`models:`,f)};const Lt={drawPersonOrSystemArray:Rt,drawBoundary:Tt,setConf:St,draw:Bt};const Yt=t=>`.person {\n    stroke: ${t.personBorder};\n    fill: ${t.personBkg};\n  }\n`;const It=Yt;const Ut={parser:p,db:et,renderer:Lt,styles:It,init:({c4:t,wrap:e})=>{Lt.setConf(t);et.setWrap(e)}}},40055:(t,e,n)=>{n.d(e,{a:()=>r,b:()=>c,c:()=>o,d:()=>s,e:()=>d,f:()=>l,g:()=>h});var i=n(16750);var a=n(76235);const s=(t,e)=>{const n=t.append("rect");n.attr("x",e.x);n.attr("y",e.y);n.attr("fill",e.fill);n.attr("stroke",e.stroke);n.attr("width",e.width);n.attr("height",e.height);e.rx!==void 0&&n.attr("rx",e.rx);e.ry!==void 0&&n.attr("ry",e.ry);if(e.attrs!==void 0){for(const t in e.attrs){n.attr(t,e.attrs[t])}}e.class!==void 0&&n.attr("class",e.class);return n};const r=(t,e)=>{const n={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};const i=s(t,n);i.lower()};const l=(t,e)=>{const n=e.text.replace(a.H," ");const i=t.append("text");i.attr("x",e.x);i.attr("y",e.y);i.attr("class","legend");i.style("text-anchor",e.anchor);e.class!==void 0&&i.attr("class",e.class);const s=i.append("tspan");s.attr("x",e.x+e.textMargin*2);s.text(n);return i};const o=(t,e,n,a)=>{const s=t.append("image");s.attr("x",e);s.attr("y",n);const r=(0,i.Jf)(a);s.attr("xlink:href",r)};const c=(t,e,n,a)=>{const s=t.append("use");s.attr("x",e);s.attr("y",n);const r=(0,i.Jf)(a);s.attr("xlink:href",`#${r}`)};const h=()=>{const t={x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0};return t};const d=()=>{const t={x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:true};return t}}}]);