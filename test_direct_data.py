#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接使用数据库数据测试AlphaStrategy策略
"""

import sys
from datetime import datetime, timedelta

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy
from vnpy_ctastrategy.base import BacktestingMode
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.database import get_database

def test_direct_data():
    """直接使用数据库数据测试策略"""
    print("开始直接数据测试AlphaStrategy...")
    
    # 获取数据库实例
    database = get_database()
    
    # 直接查询cu2509.SHFE的数据
    symbol = "cu2509"
    exchange = Exchange.SHFE
    
    # 查询所有可用数据
    end_date = datetime.now()
    start_date = datetime(2024, 9, 1)  # 从9月开始
    
    bars = database.load_bar_data(
        symbol=symbol,
        exchange=exchange,
        interval=Interval.DAILY,
        start=start_date,
        end=end_date
    )
    
    if not bars:
        print("没有找到数据！")
        return
    
    print(f"找到 {len(bars)} 条数据")
    print(f"数据范围: {bars[0].datetime} 到 {bars[-1].datetime}")
    print(f"价格范围: {min(bar.close_price for bar in bars):.2f} - {max(bar.close_price for bar in bars):.2f}")
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 使用实际数据的时间范围
    actual_start = bars[0].datetime
    actual_end = bars[-1].datetime
    
    # 确保有足够的数据进行回测
    if len(bars) < 50:
        print(f"数据不足，只有{len(bars)}条数据，建议至少50条")
        return
    
    # 使用最后60天的数据进行回测
    backtest_start = actual_end - timedelta(days=60)
    backtest_bars = [bar for bar in bars if bar.datetime >= backtest_start]
    
    if len(backtest_bars) < 20:
        # 如果最后60天数据不足，使用最后30条数据
        backtest_bars = bars[-30:]
        backtest_start = backtest_bars[0].datetime
    
    print(f"回测数据: {len(backtest_bars)} 条，从 {backtest_start} 到 {actual_end}")
    
    # 设置回测参数
    engine.set_parameters(
        vt_symbol=f"{symbol}.{exchange.value}",
        interval=Interval.DAILY,
        start=backtest_start,
        end=actual_end,
        rate=0.0002,
        slippage=0.001,
        size=1,
        pricetick=1.0,  # 铜期货最小变动单位
        capital=100000,
        mode=BacktestingMode.BAR
    )
    
    # 策略参数 - 使用更敏感的设置
    strategy_setting = {
        "alpha_factors": ["alpha001", "alpha015", "alpha030"],
        "factor_weights": [0.33, 0.33, 0.34],
        "signal_threshold": 0.01,  # 进一步降低阈值
        "lookback_days": 5,  # 使用更短的回看期
        "debug_mode": True,
        "debug_interval": 1,  # 每条数据都输出调试信息
        "risk_percent": 0.05,  # 增加风险比例
        "max_position": 3
    }
    
    # 添加策略
    engine.add_strategy(AlphaStrategy, strategy_setting)
    
    # 手动设置历史数据
    engine.history_data = backtest_bars
    
    print("开始回测...")
    
    try:
        # 运行回测
        engine.run_backtesting()
        
        # 输出结果
        print("\n" + "="*60)
        print("回测结果:")
        print("="*60)
        
        if engine.trades:
            print(f"总交易次数: {len(engine.trades)}")
            print("\n交易记录:")
            for i, (trade_id, trade) in enumerate(list(engine.trades.items())):
                direction = "买入" if trade.direction.value == "多" else "卖出"
                print(f"  {i+1}. {trade.datetime.strftime('%Y-%m-%d')} {direction} {trade.volume}手 @ {trade.price:.2f}")
        else:
            print("没有产生交易记录")
        
        # 输出所有策略日志
        print(f"\n策略完整日志:")
        for log in engine.logs:
            if any(keyword in log for keyword in ['Alpha', '信号', '开多仓', '开空仓', '数据检查', '初始化', '启动']):
                print(f"  {log}")
        
        # 计算结果
        try:
            df = engine.calculate_result()
            if df is not None and not df.empty:
                print(f"\n回测统计:")
                if 'net_pnl' in df.columns:
                    final_pnl = df['net_pnl'].iloc[-1]
                    print(f"最终盈亏: {final_pnl:.2f}")
        except Exception as e:
            print(f"计算统计时出错: {e}")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_data()
