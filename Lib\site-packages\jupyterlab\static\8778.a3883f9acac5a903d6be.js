(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8778,5606],{65606:e=>{var t=e.exports={};var r;var n;function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){r=setTimeout}else{r=s}}catch(e){r=s}try{if(typeof clearTimeout==="function"){n=clearTimeout}else{n=o}}catch(e){n=o}})();function a(e){if(r===setTimeout){return setTimeout(e,0)}if((r===s||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function i(e){if(n===clearTimeout){return clearTimeout(e)}if((n===o||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var l=[];var c=false;var u;var p=-1;function f(){if(!c||!u){return}c=false;if(u.length){l=u.concat(l)}else{p=-1}if(l.length){h()}}function h(){if(c){return}var e=a(f);c=true;var t=l.length;while(t){u=l;l=[];while(++p<t){if(u){u[p].run()}}p=-1;t=l.length}u=null;c=false;i(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}l.push(new E(e,t));if(l.length===1&&!c){a(h)}};function E(e,t){this.fun=e;this.array=t}E.prototype.run=function(){this.fun.apply(null,this.array)};t.title="browser";t.browser=true;t.env={};t.argv=[];t.version="";t.versions={};function m(){}t.on=m;t.addListener=m;t.once=m;t.off=m;t.removeListener=m;t.removeAllListeners=m;t.emit=m;t.prependListener=m;t.prependOnceListener=m;t.listeners=function(e){return[]};t.binding=function(e){throw new Error("process.binding is not supported")};t.cwd=function(){return"/"};t.chdir=function(e){throw new Error("process.chdir is not supported")};t.umask=function(){return 0}},49764:(e,t,r)=>{!function(t,n){true?e.exports=n(r(44914)):0}(r.g,(function(e){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(n,s,function(t){return e[t]}.bind(null,s));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=4)}([function(e,t,r){e.exports=r(2)()},function(t,r){t.exports=e},function(e,t,r){"use strict";var n=r(3);function s(){}function o(){}o.resetWarningCache=s,e.exports=function(){function e(e,t,r,s,o,a){if(a!==n){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:s};return r.PropTypes=r,r}},function(e,t,r){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,r,n){"use strict";n.r(r);var s=n(1),o=n.n(s),a=n(0),i=n.n(a);function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var c=function(e){var t=e.pageClassName,r=e.pageLinkClassName,n=e.page,s=e.selected,a=e.activeClassName,i=e.activeLinkClassName,c=e.getEventListener,u=e.pageSelectedHandler,p=e.href,f=e.extraAriaContext,h=e.ariaLabel||"Page "+n+(f?" "+f:""),E=null;return s&&(E="page",h=e.ariaLabel||"Page "+n+" is your current page",t=void 0!==t?t+" "+a:a,void 0!==r?void 0!==i&&(r=r+" "+i):r=i),o.a.createElement("li",{className:t},o.a.createElement("a",l({role:"button",className:r,href:p,tabIndex:"0","aria-label":h,"aria-current":E,onKeyPress:u},c(u)),n))};c.propTypes={pageSelectedHandler:i.a.func.isRequired,selected:i.a.bool.isRequired,pageClassName:i.a.string,pageLinkClassName:i.a.string,activeClassName:i.a.string,activeLinkClassName:i.a.string,extraAriaContext:i.a.string,href:i.a.string,ariaLabel:i.a.string,page:i.a.number.isRequired,getEventListener:i.a.func.isRequired};var u=c;function p(){return(p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}!function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var o=void 0;try{o=n[s]}catch(e){continue}e.register(o,s,"/home/<USER>/workspace/react-paginate/react_components/PageView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/PageView.js")}}();var f=function(e){var t=e.breakLabel,r=e.breakClassName,n=e.breakLinkClassName,s=e.breakHandler,a=e.getEventListener,i=r||"break";return o.a.createElement("li",{className:i},o.a.createElement("a",p({className:n,role:"button",tabIndex:"0",onKeyPress:s},a(s)),t))};f.propTypes={breakLabel:i.a.oneOfType([i.a.string,i.a.node]),breakClassName:i.a.string,breakLinkClassName:i.a.string,breakHandler:i.a.func.isRequired,getEventListener:i.a.func.isRequired};var h=f;function E(e){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(){return(m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function g(e,t){return(g=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=$(e);if(t){var s=$(this).constructor;r=Reflect.construct(n,arguments,s)}else r=n.apply(this,arguments);return L(this,r)}}function L(e,t){return!t||"object"!==E(t)&&"function"!=typeof t?R(e):t}function R(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $(e){return($=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function N(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var o=void 0;try{o=n[s]}catch(e){continue}e.register(o,s,"/home/<USER>/workspace/react-paginate/react_components/BreakView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/BreakView.js")}}();var I=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&g(e,t)}(a,e);var t,r,n,s=v(a);function a(e){var t,r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),N(R(t=s.call(this,e)),"handlePreviousPage",(function(e){var r=t.state.selected;e.preventDefault?e.preventDefault():e.returnValue=!1,r>0&&t.handlePageSelected(r-1,e)})),N(R(t),"handleNextPage",(function(e){var r=t.state.selected,n=t.props.pageCount;e.preventDefault?e.preventDefault():e.returnValue=!1,r<n-1&&t.handlePageSelected(r+1,e)})),N(R(t),"handlePageSelected",(function(e,r){r.preventDefault?r.preventDefault():r.returnValue=!1,t.state.selected!==e&&(t.setState({selected:e}),t.callCallback(e))})),N(R(t),"getEventListener",(function(e){return N({},t.props.eventListener,e)})),N(R(t),"handleBreakClick",(function(e,r){r.preventDefault?r.preventDefault():r.returnValue=!1;var n=t.state.selected;t.handlePageSelected(n<e?t.getForwardJump():t.getBackwardJump(),r)})),N(R(t),"callCallback",(function(e){void 0!==t.props.onPageChange&&"function"==typeof t.props.onPageChange&&t.props.onPageChange({selected:e})})),N(R(t),"pagination",(function(){var e=[],r=t.props,n=r.pageRangeDisplayed,s=r.pageCount,a=r.marginPagesDisplayed,i=r.breakLabel,l=r.breakClassName,c=r.breakLinkClassName,u=t.state.selected;if(s<=n)for(var p=0;p<s;p++)e.push(t.getPageElement(p));else{var f,E,m,d=n/2,g=n-d;u>s-n/2?d=n-(g=s-u):u<n/2&&(g=n-(d=u));var v=function(e){return t.getPageElement(e)};for(f=0;f<s;f++)(E=f+1)<=a||E>s-a||f>=u-d&&f<=u+g?e.push(v(f)):i&&e[e.length-1]!==m&&(m=o.a.createElement(h,{key:f,breakLabel:i,breakClassName:l,breakLinkClassName:c,breakHandler:t.handleBreakClick.bind(null,f),getEventListener:t.getEventListener}),e.push(m))}return e})),r=e.initialPage?e.initialPage:e.forcePage?e.forcePage:0,t.state={selected:r},t}return t=a,(r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.initialPage,r=e.disableInitialCallback,n=e.extraAriaContext;void 0===t||r||this.callCallback(t),n&&console.warn("DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead.")}},{key:"componentDidUpdate",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&this.setState({selected:this.props.forcePage})}},{key:"getForwardJump",value:function(){var e=this.state.selected,t=this.props,r=t.pageCount,n=e+t.pageRangeDisplayed;return n>=r?r-1:n}},{key:"getBackwardJump",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:"hrefBuilder",value:function(e){var t=this.props,r=t.hrefBuilder,n=t.pageCount;if(r&&e!==this.state.selected&&e>=0&&e<n)return r(e+1)}},{key:"ariaLabelBuilder",value:function(e){var t=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var r=this.props.ariaLabelBuilder(e+1,t);return this.props.extraAriaContext&&!t&&(r=r+" "+this.props.extraAriaContext),r}}},{key:"getPageElement",value:function(e){var t=this.state.selected,r=this.props,n=r.pageClassName,s=r.pageLinkClassName,a=r.activeClassName,i=r.activeLinkClassName,l=r.extraAriaContext;return o.a.createElement(u,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:t===e,pageClassName:n,pageLinkClassName:s,activeClassName:a,activeLinkClassName:i,extraAriaContext:l,href:this.hrefBuilder(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,getEventListener:this.getEventListener})}},{key:"render",value:function(){var e=this.props,t=e.disabledClassName,r=e.pageCount,n=e.containerClassName,s=e.previousLabel,a=e.previousClassName,i=e.previousLinkClassName,l=e.previousAriaLabel,c=e.nextLabel,u=e.nextClassName,p=e.nextLinkClassName,f=e.nextAriaLabel,h=this.state.selected,E=a+(0===h?" ".concat(t):""),d=u+(h===r-1?" ".concat(t):""),g=0===h?"true":"false",v=h===r-1?"true":"false";return o.a.createElement("ul",{className:n},o.a.createElement("li",{className:E},o.a.createElement("a",m({className:i,href:this.hrefBuilder(h-1),tabIndex:"0",role:"button",onKeyPress:this.handlePreviousPage,"aria-disabled":g,"aria-label":l},this.getEventListener(this.handlePreviousPage)),s)),this.pagination(),o.a.createElement("li",{className:d},o.a.createElement("a",m({className:p,href:this.hrefBuilder(h+1),tabIndex:"0",role:"button",onKeyPress:this.handleNextPage,"aria-disabled":v,"aria-label":f},this.getEventListener(this.handleNextPage)),c)))}}])&&d(t.prototype,r),n&&d(t,n),a}(s.Component);N(I,"propTypes",{pageCount:i.a.number.isRequired,pageRangeDisplayed:i.a.number.isRequired,marginPagesDisplayed:i.a.number.isRequired,previousLabel:i.a.node,previousAriaLabel:i.a.string,nextLabel:i.a.node,nextAriaLabel:i.a.string,breakLabel:i.a.oneOfType([i.a.string,i.a.node]),hrefBuilder:i.a.func,onPageChange:i.a.func,initialPage:i.a.number,forcePage:i.a.number,disableInitialCallback:i.a.bool,containerClassName:i.a.string,pageClassName:i.a.string,pageLinkClassName:i.a.string,activeClassName:i.a.string,activeLinkClassName:i.a.string,previousClassName:i.a.string,nextClassName:i.a.string,previousLinkClassName:i.a.string,nextLinkClassName:i.a.string,disabledClassName:i.a.string,breakClassName:i.a.string,breakLinkClassName:i.a.string,extraAriaContext:i.a.string,ariaLabelBuilder:i.a.func,eventListener:i.a.string}),N(I,"defaultProps",{pageCount:10,pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:"selected",previousLabel:"Previous",previousClassName:"previous",previousAriaLabel:"Previous page",nextLabel:"Next",nextClassName:"next",nextAriaLabel:"Next page",breakLabel:"...",disabledClassName:"disabled",disableInitialCallback:!1,eventListener:"onClick"}),function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var o=void 0;try{o=n[s]}catch(e){continue}e.register(o,s,"/home/<USER>/workspace/react-paginate/react_components/PaginationBoxView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/PaginationBoxView.js")}}();r.default=I;!function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var o=void 0;try{o=n[s]}catch(e){continue}e.register(o,s,"/home/<USER>/workspace/react-paginate/react_components/index.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/index.js")}}()}])}))},93904:(e,t,r)=>{const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){t=o(t);if(e instanceof s){if(e.loose===!!t.loose){return e}else{e=e.value}}e=e.trim().split(/\s+/).join(" ");c("comparator",e,t);this.options=t;this.loose=!!t.loose;this.parse(e);if(this.semver===n){this.value=""}else{this.value=this.operator+this.semver.version}c("comp",this)}parse(e){const t=this.options.loose?a[i.COMPARATORLOOSE]:a[i.COMPARATOR];const r=e.match(t);if(!r){throw new TypeError(`Invalid comparator: ${e}`)}this.operator=r[1]!==undefined?r[1]:"";if(this.operator==="="){this.operator=""}if(!r[2]){this.semver=n}else{this.semver=new u(r[2],this.options.loose)}}toString(){return this.value}test(e){c("Comparator.test",e,this.options.loose);if(this.semver===n||e===n){return true}if(typeof e==="string"){try{e=new u(e,this.options)}catch(t){return false}}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s)){throw new TypeError("a Comparator is required")}if(this.operator===""){if(this.value===""){return true}return new p(e.value,t).test(this.value)}else if(e.operator===""){if(e.value===""){return true}return new p(this.value,t).test(e.semver)}t=o(t);if(t.includePrerelease&&(this.value==="<0.0.0-0"||e.value==="<0.0.0-0")){return false}if(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))){return false}if(this.operator.startsWith(">")&&e.operator.startsWith(">")){return true}if(this.operator.startsWith("<")&&e.operator.startsWith("<")){return true}if(this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")){return true}if(l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")){return true}if(l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")){return true}return false}}e.exports=s;const o=r(98587);const{safeRe:a,t:i}=r(99718);const l=r(72111);const c=r(57272);const u=r(31527);const p=r(78311)},78311:(e,t,r)=>{const n=/\s+/g;class s{constructor(e,t){t=i(t);if(e instanceof s){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease){return e}else{return new s(e.raw,t)}}if(e instanceof l){this.raw=e.value;this.set=[[e]];this.formatted=undefined;return this}this.options=t;this.loose=!!t.loose;this.includePrerelease=!!t.includePrerelease;this.raw=e.trim().replace(n," ");this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length));if(!this.set.length){throw new TypeError(`Invalid SemVer Range: ${this.raw}`)}if(this.set.length>1){const e=this.set[0];this.set=this.set.filter((e=>!v(e[0])));if(this.set.length===0){this.set=[e]}else if(this.set.length>1){for(const e of this.set){if(e.length===1&&L(e[0])){this.set=[e];break}}}}this.formatted=undefined}get range(){if(this.formatted===undefined){this.formatted="";for(let e=0;e<this.set.length;e++){if(e>0){this.formatted+="||"}const t=this.set[e];for(let e=0;e<t.length;e++){if(e>0){this.formatted+=" "}this.formatted+=t[e].toString().trim()}}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=(this.options.includePrerelease&&d)|(this.options.loose&&g);const r=t+":"+e;const n=a.get(r);if(n){return n}const s=this.options.loose;const o=s?p[f.HYPHENRANGELOOSE]:p[f.HYPHENRANGE];e=e.replace(o,A(this.options.includePrerelease));c("hyphen replace",e);e=e.replace(p[f.COMPARATORTRIM],h);c("comparator trim",e);e=e.replace(p[f.TILDETRIM],E);c("tilde trim",e);e=e.replace(p[f.CARETTRIM],m);c("caret trim",e);let i=e.split(" ").map((e=>$(e,this.options))).join(" ").split(/\s+/).map((e=>C(e,this.options)));if(s){i=i.filter((e=>{c("loose invalid filter",e,this.options);return!!e.match(p[f.COMPARATORLOOSE])}))}c("range list",i);const u=new Map;const L=i.map((e=>new l(e,this.options)));for(const a of L){if(v(a)){return[a]}u.set(a.value,a)}if(u.size>1&&u.has("")){u.delete("")}const R=[...u.values()];a.set(r,R);return R}intersects(e,t){if(!(e instanceof s)){throw new TypeError("a Range is required")}return this.set.some((r=>R(r,t)&&e.set.some((e=>R(e,t)&&r.every((r=>e.every((e=>r.intersects(e,t)))))))))}test(e){if(!e){return false}if(typeof e==="string"){try{e=new u(e,this.options)}catch(t){return false}}for(let r=0;r<this.set.length;r++){if(S(this.set[r],e,this.options)){return true}}return false}}e.exports=s;const o=r(68794);const a=new o;const i=r(98587);const l=r(93904);const c=r(57272);const u=r(31527);const{safeRe:p,t:f,comparatorTrimReplace:h,tildeTrimReplace:E,caretTrimReplace:m}=r(99718);const{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:g}=r(16874);const v=e=>e.value==="<0.0.0-0";const L=e=>e.value==="";const R=(e,t)=>{let r=true;const n=e.slice();let s=n.pop();while(r&&n.length){r=n.every((e=>s.intersects(e,t)));s=n.pop()}return r};const $=(e,t)=>{c("comp",e,t);e=O(e,t);c("caret",e);e=I(e,t);c("tildes",e);e=T(e,t);c("xrange",e);e=w(e,t);c("stars",e);return e};const N=e=>!e||e.toLowerCase()==="x"||e==="*";const I=(e,t)=>e.trim().split(/\s+/).map((e=>b(e,t))).join(" ");const b=(e,t)=>{const r=t.loose?p[f.TILDELOOSE]:p[f.TILDE];return e.replace(r,((t,r,n,s,o)=>{c("tilde",e,t,r,n,s,o);let a;if(N(r)){a=""}else if(N(n)){a=`>=${r}.0.0 <${+r+1}.0.0-0`}else if(N(s)){a=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`}else if(o){c("replaceTilde pr",o);a=`>=${r}.${n}.${s}-${o} <${r}.${+n+1}.0-0`}else{a=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`}c("tilde return",a);return a}))};const O=(e,t)=>e.trim().split(/\s+/).map((e=>y(e,t))).join(" ");const y=(e,t)=>{c("caret",e,t);const r=t.loose?p[f.CARETLOOSE]:p[f.CARET];const n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,s,o,a)=>{c("caret",e,t,r,s,o,a);let i;if(N(r)){i=""}else if(N(s)){i=`>=${r}.0.0${n} <${+r+1}.0.0-0`}else if(N(o)){if(r==="0"){i=`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`}else{i=`>=${r}.${s}.0${n} <${+r+1}.0.0-0`}}else if(a){c("replaceCaret pr",a);if(r==="0"){if(s==="0"){i=`>=${r}.${s}.${o}-${a} <${r}.${s}.${+o+1}-0`}else{i=`>=${r}.${s}.${o}-${a} <${r}.${+s+1}.0-0`}}else{i=`>=${r}.${s}.${o}-${a} <${+r+1}.0.0-0`}}else{c("no pr");if(r==="0"){if(s==="0"){i=`>=${r}.${s}.${o}${n} <${r}.${s}.${+o+1}-0`}else{i=`>=${r}.${s}.${o}${n} <${r}.${+s+1}.0-0`}}else{i=`>=${r}.${s}.${o} <${+r+1}.0.0-0`}}c("caret return",i);return i}))};const T=(e,t)=>{c("replaceXRanges",e,t);return e.split(/\s+/).map((e=>P(e,t))).join(" ")};const P=(e,t)=>{e=e.trim();const r=t.loose?p[f.XRANGELOOSE]:p[f.XRANGE];return e.replace(r,((r,n,s,o,a,i)=>{c("xRange",e,r,n,s,o,a,i);const l=N(s);const u=l||N(o);const p=u||N(a);const f=p;if(n==="="&&f){n=""}i=t.includePrerelease?"-0":"";if(l){if(n===">"||n==="<"){r="<0.0.0-0"}else{r="*"}}else if(n&&f){if(u){o=0}a=0;if(n===">"){n=">=";if(u){s=+s+1;o=0;a=0}else{o=+o+1;a=0}}else if(n==="<="){n="<";if(u){s=+s+1}else{o=+o+1}}if(n==="<"){i="-0"}r=`${n+s}.${o}.${a}${i}`}else if(u){r=`>=${s}.0.0${i} <${+s+1}.0.0-0`}else if(p){r=`>=${s}.${o}.0${i} <${s}.${+o+1}.0-0`}c("xRange return",r);return r}))};const w=(e,t)=>{c("replaceStars",e,t);return e.trim().replace(p[f.STAR],"")};const C=(e,t)=>{c("replaceGTE0",e,t);return e.trim().replace(p[t.includePrerelease?f.GTE0PRE:f.GTE0],"")};const A=e=>(t,r,n,s,o,a,i,l,c,u,p,f)=>{if(N(n)){r=""}else if(N(s)){r=`>=${n}.0.0${e?"-0":""}`}else if(N(o)){r=`>=${n}.${s}.0${e?"-0":""}`}else if(a){r=`>=${r}`}else{r=`>=${r}${e?"-0":""}`}if(N(c)){l=""}else if(N(u)){l=`<${+c+1}.0.0-0`}else if(N(p)){l=`<${c}.${+u+1}.0-0`}else if(f){l=`<=${c}.${u}.${p}-${f}`}else if(e){l=`<${c}.${u}.${+p+1}-0`}else{l=`<=${l}`}return`${r} ${l}`.trim()};const S=(e,t,r)=>{for(let n=0;n<e.length;n++){if(!e[n].test(t)){return false}}if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++){c(e[r].semver);if(e[r].semver===l.ANY){continue}if(e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch){return true}}}return false}return true}},31527:(e,t,r)=>{const n=r(57272);const{MAX_LENGTH:s,MAX_SAFE_INTEGER:o}=r(16874);const{safeRe:a,t:i}=r(99718);const l=r(98587);const{compareIdentifiers:c}=r(61123);class u{constructor(e,t){t=l(t);if(e instanceof u){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease){return e}else{e=e.version}}else if(typeof e!=="string"){throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`)}if(e.length>s){throw new TypeError(`version is longer than ${s} characters`)}n("SemVer",e,t);this.options=t;this.loose=!!t.loose;this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?a[i.LOOSE]:a[i.FULL]);if(!r){throw new TypeError(`Invalid Version: ${e}`)}this.raw=e;this.major=+r[1];this.minor=+r[2];this.patch=+r[3];if(this.major>o||this.major<0){throw new TypeError("Invalid major version")}if(this.minor>o||this.minor<0){throw new TypeError("Invalid minor version")}if(this.patch>o||this.patch<0){throw new TypeError("Invalid patch version")}if(!r[4]){this.prerelease=[]}else{this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<o){return t}}return e}))}this.build=r[5]?r[5].split("."):[];this.format()}format(){this.version=`${this.major}.${this.minor}.${this.patch}`;if(this.prerelease.length){this.version+=`-${this.prerelease.join(".")}`}return this.version}toString(){return this.version}compare(e){n("SemVer.compare",this.version,this.options,e);if(!(e instanceof u)){if(typeof e==="string"&&e===this.version){return 0}e=new u(e,this.options)}if(e.version===this.version){return 0}return this.compareMain(e)||this.comparePre(e)}compareMain(e){if(!(e instanceof u)){e=new u(e,this.options)}return c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(!(e instanceof u)){e=new u(e,this.options)}if(this.prerelease.length&&!e.prerelease.length){return-1}else if(!this.prerelease.length&&e.prerelease.length){return 1}else if(!this.prerelease.length&&!e.prerelease.length){return 0}let t=0;do{const r=this.prerelease[t];const s=e.prerelease[t];n("prerelease compare",t,r,s);if(r===undefined&&s===undefined){return 0}else if(s===undefined){return 1}else if(r===undefined){return-1}else if(r===s){continue}else{return c(r,s)}}while(++t)}compareBuild(e){if(!(e instanceof u)){e=new u(e,this.options)}let t=0;do{const r=this.build[t];const s=e.build[t];n("build compare",t,r,s);if(r===undefined&&s===undefined){return 0}else if(s===undefined){return 1}else if(r===undefined){return-1}else if(r===s){continue}else{return c(r,s)}}while(++t)}inc(e,t,r){switch(e){case"premajor":this.prerelease.length=0;this.patch=0;this.minor=0;this.major++;this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0;this.patch=0;this.minor++;this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0;this.inc("patch",t,r);this.inc("pre",t,r);break;case"prerelease":if(this.prerelease.length===0){this.inc("patch",t,r)}this.inc("pre",t,r);break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0){this.major++}this.minor=0;this.patch=0;this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0){this.minor++}this.patch=0;this.prerelease=[];break;case"patch":if(this.prerelease.length===0){this.patch++}this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(!t&&r===false){throw new Error("invalid increment argument: identifier is empty")}if(this.prerelease.length===0){this.prerelease=[e]}else{let n=this.prerelease.length;while(--n>=0){if(typeof this.prerelease[n]==="number"){this.prerelease[n]++;n=-2}}if(n===-1){if(t===this.prerelease.join(".")&&r===false){throw new Error("invalid increment argument: identifier already exists")}this.prerelease.push(e)}}if(t){let n=[t,e];if(r===false){n=[t]}if(c(this.prerelease[0],t)===0){if(isNaN(this.prerelease[1])){this.prerelease=n}}else{this.prerelease=n}}break}default:throw new Error(`invalid increment argument: ${e}`)}this.raw=this.format();if(this.build.length){this.raw+=`+${this.build.join(".")}`}return this}}e.exports=u},57414:(e,t,r)=>{const n=r(30144);const s=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null};e.exports=s},72111:(e,t,r)=>{const n=r(94641);const s=r(13999);const o=r(35580);const a=r(54089);const i=r(7059);const l=r(25200);const c=(e,t,r,c)=>{switch(t){case"===":if(typeof e==="object"){e=e.version}if(typeof r==="object"){r=r.version}return e===r;case"!==":if(typeof e==="object"){e=e.version}if(typeof r==="object"){r=r.version}return e!==r;case"":case"=":case"==":return n(e,r,c);case"!=":return s(e,r,c);case">":return o(e,r,c);case">=":return a(e,r,c);case"<":return i(e,r,c);case"<=":return l(e,r,c);default:throw new TypeError(`Invalid operator: ${t}`)}};e.exports=c},46170:(e,t,r)=>{const n=r(31527);const s=r(30144);const{safeRe:o,t:a}=r(99718);const i=(e,t)=>{if(e instanceof n){return e}if(typeof e==="number"){e=String(e)}if(typeof e!=="string"){return null}t=t||{};let r=null;if(!t.rtl){r=e.match(t.includePrerelease?o[a.COERCEFULL]:o[a.COERCE])}else{const n=t.includePrerelease?o[a.COERCERTLFULL]:o[a.COERCERTL];let s;while((s=n.exec(e))&&(!r||r.index+r[0].length!==e.length)){if(!r||s.index+s[0].length!==r.index+r[0].length){r=s}n.lastIndex=s.index+s[1].length+s[2].length}n.lastIndex=-1}if(r===null){return null}const i=r[2];const l=r[3]||"0";const c=r[4]||"0";const u=t.includePrerelease&&r[5]?`-${r[5]}`:"";const p=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${i}.${l}.${c}${u}${p}`,t)};e.exports=i},40909:(e,t,r)=>{const n=r(31527);const s=(e,t,r)=>{const s=new n(e,r);const o=new n(t,r);return s.compare(o)||s.compareBuild(o)};e.exports=s},11763:(e,t,r)=>{const n=r(50560);const s=(e,t)=>n(e,t,true);e.exports=s},50560:(e,t,r)=>{const n=r(31527);const s=(e,t,r)=>new n(e,r).compare(new n(t,r));e.exports=s},51832:(e,t,r)=>{const n=r(30144);const s=(e,t)=>{const r=n(e,null,true);const s=n(t,null,true);const o=r.compare(s);if(o===0){return null}const a=o>0;const i=a?r:s;const l=a?s:r;const c=!!i.prerelease.length;const u=!!l.prerelease.length;if(u&&!c){if(!l.patch&&!l.minor){return"major"}if(i.patch){return"patch"}if(i.minor){return"minor"}return"major"}const p=c?"pre":"";if(r.major!==s.major){return p+"major"}if(r.minor!==s.minor){return p+"minor"}if(r.patch!==s.patch){return p+"patch"}return"prerelease"};e.exports=s},94641:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)===0;e.exports=s},35580:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)>0;e.exports=s},54089:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)>=0;e.exports=s},93007:(e,t,r)=>{const n=r(31527);const s=(e,t,r,s,o)=>{if(typeof r==="string"){o=s;s=r;r=undefined}try{return new n(e instanceof n?e.version:e,r).inc(t,s,o).version}catch(a){return null}};e.exports=s},7059:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)<0;e.exports=s},25200:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)<=0;e.exports=s},32938:(e,t,r)=>{const n=r(31527);const s=(e,t)=>new n(e,t).major;e.exports=s},46254:(e,t,r)=>{const n=r(31527);const s=(e,t)=>new n(e,t).minor;e.exports=s},13999:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(e,t,r)!==0;e.exports=s},30144:(e,t,r)=>{const n=r(31527);const s=(e,t,r=false)=>{if(e instanceof n){return e}try{return new n(e,t)}catch(s){if(!r){return null}throw s}};e.exports=s},24493:(e,t,r)=>{const n=r(31527);const s=(e,t)=>new n(e,t).patch;e.exports=s},31729:(e,t,r)=>{const n=r(30144);const s=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null};e.exports=s},9970:(e,t,r)=>{const n=r(50560);const s=(e,t,r)=>n(t,e,r);e.exports=s},74277:(e,t,r)=>{const n=r(40909);const s=(e,t)=>e.sort(((e,r)=>n(r,e,t)));e.exports=s},97638:(e,t,r)=>{const n=r(78311);const s=(e,t,r)=>{try{t=new n(t,r)}catch(s){return false}return t.test(e)};e.exports=s},43927:(e,t,r)=>{const n=r(40909);const s=(e,t)=>e.sort(((e,r)=>n(e,r,t)));e.exports=s},56953:(e,t,r)=>{const n=r(30144);const s=(e,t)=>{const r=n(e,t);return r?r.version:null};e.exports=s},99589:(e,t,r)=>{const n=r(99718);const s=r(16874);const o=r(31527);const a=r(61123);const i=r(30144);const l=r(56953);const c=r(57414);const u=r(93007);const p=r(51832);const f=r(32938);const h=r(46254);const E=r(24493);const m=r(31729);const d=r(50560);const g=r(9970);const v=r(11763);const L=r(40909);const R=r(43927);const $=r(74277);const N=r(35580);const I=r(7059);const b=r(94641);const O=r(13999);const y=r(54089);const T=r(25200);const P=r(72111);const w=r(46170);const C=r(93904);const A=r(78311);const S=r(97638);const x=r(77631);const k=r(19628);const j=r(270);const D=r(41261);const _=r(13874);const G=r(97075);const M=r(75571);const F=r(5342);const U=r(76780);const X=r(72525);const B=r(75032);e.exports={parse:i,valid:l,clean:c,inc:u,diff:p,major:f,minor:h,patch:E,prerelease:m,compare:d,rcompare:g,compareLoose:v,compareBuild:L,sort:R,rsort:$,gt:N,lt:I,eq:b,neq:O,gte:y,lte:T,cmp:P,coerce:w,Comparator:C,Range:A,satisfies:S,toComparators:x,maxSatisfying:k,minSatisfying:j,minVersion:D,validRange:_,outside:G,gtr:M,ltr:F,intersects:U,simplifyRange:X,subset:B,SemVer:o,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:a.compareIdentifiers,rcompareIdentifiers:a.rcompareIdentifiers}},16874:e=>{const t="2.0.0";const r=256;const n=Number.MAX_SAFE_INTEGER||9007199254740991;const s=16;const o=r-6;const a=["major","premajor","minor","preminor","patch","prepatch","prerelease"];e.exports={MAX_LENGTH:r,MAX_SAFE_COMPONENT_LENGTH:s,MAX_SAFE_BUILD_LENGTH:o,MAX_SAFE_INTEGER:n,RELEASE_TYPES:a,SEMVER_SPEC_VERSION:t,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},57272:(e,t,r)=>{var n=r(65606);const s=typeof n==="object"&&n.env&&n.env.NODE_DEBUG&&/\bsemver\b/i.test(n.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=s},61123:e=>{const t=/^[0-9]+$/;const r=(e,r)=>{const n=t.test(e);const s=t.test(r);if(n&&s){e=+e;r=+r}return e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};const n=(e,t)=>r(t,e);e.exports={compareIdentifiers:r,rcompareIdentifiers:n}},68794:e=>{class t{constructor(){this.max=1e3;this.map=new Map}get(e){const t=this.map.get(e);if(t===undefined){return undefined}else{this.map.delete(e);this.map.set(e,t);return t}}delete(e){return this.map.delete(e)}set(e,t){const r=this.delete(e);if(!r&&t!==undefined){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},98587:e=>{const t=Object.freeze({loose:true});const r=Object.freeze({});const n=e=>{if(!e){return r}if(typeof e!=="object"){return t}return e};e.exports=n},99718:(e,t,r)=>{const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:o}=r(16874);const a=r(57272);t=e.exports={};const i=t.re=[];const l=t.safeRe=[];const c=t.src=[];const u=t.t={};let p=0;const f="[a-zA-Z0-9-]";const h=[["\\s",1],["\\d",o],[f,s]];const E=e=>{for(const[t,r]of h){e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`)}return e};const m=(e,t,r)=>{const n=E(t);const s=p++;a(e,s,t);u[e]=s;c[s]=t;i[s]=new RegExp(t,r?"g":undefined);l[s]=new RegExp(n,r?"g":undefined)};m("NUMERICIDENTIFIER","0|[1-9]\\d*");m("NUMERICIDENTIFIERLOOSE","\\d+");m("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`);m("MAINVERSION",`(${c[u.NUMERICIDENTIFIER]})\\.`+`(${c[u.NUMERICIDENTIFIER]})\\.`+`(${c[u.NUMERICIDENTIFIER]})`);m("MAINVERSIONLOOSE",`(${c[u.NUMERICIDENTIFIERLOOSE]})\\.`+`(${c[u.NUMERICIDENTIFIERLOOSE]})\\.`+`(${c[u.NUMERICIDENTIFIERLOOSE]})`);m("PRERELEASEIDENTIFIER",`(?:${c[u.NUMERICIDENTIFIER]}|${c[u.NONNUMERICIDENTIFIER]})`);m("PRERELEASEIDENTIFIERLOOSE",`(?:${c[u.NUMERICIDENTIFIERLOOSE]}|${c[u.NONNUMERICIDENTIFIER]})`);m("PRERELEASE",`(?:-(${c[u.PRERELEASEIDENTIFIER]}(?:\\.${c[u.PRERELEASEIDENTIFIER]})*))`);m("PRERELEASELOOSE",`(?:-?(${c[u.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[u.PRERELEASEIDENTIFIERLOOSE]})*))`);m("BUILDIDENTIFIER",`${f}+`);m("BUILD",`(?:\\+(${c[u.BUILDIDENTIFIER]}(?:\\.${c[u.BUILDIDENTIFIER]})*))`);m("FULLPLAIN",`v?${c[u.MAINVERSION]}${c[u.PRERELEASE]}?${c[u.BUILD]}?`);m("FULL",`^${c[u.FULLPLAIN]}$`);m("LOOSEPLAIN",`[v=\\s]*${c[u.MAINVERSIONLOOSE]}${c[u.PRERELEASELOOSE]}?${c[u.BUILD]}?`);m("LOOSE",`^${c[u.LOOSEPLAIN]}$`);m("GTLT","((?:<|>)?=?)");m("XRANGEIDENTIFIERLOOSE",`${c[u.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);m("XRANGEIDENTIFIER",`${c[u.NUMERICIDENTIFIER]}|x|X|\\*`);m("XRANGEPLAIN",`[v=\\s]*(${c[u.XRANGEIDENTIFIER]})`+`(?:\\.(${c[u.XRANGEIDENTIFIER]})`+`(?:\\.(${c[u.XRANGEIDENTIFIER]})`+`(?:${c[u.PRERELEASE]})?${c[u.BUILD]}?`+`)?)?`);m("XRANGEPLAINLOOSE",`[v=\\s]*(${c[u.XRANGEIDENTIFIERLOOSE]})`+`(?:\\.(${c[u.XRANGEIDENTIFIERLOOSE]})`+`(?:\\.(${c[u.XRANGEIDENTIFIERLOOSE]})`+`(?:${c[u.PRERELEASELOOSE]})?${c[u.BUILD]}?`+`)?)?`);m("XRANGE",`^${c[u.GTLT]}\\s*${c[u.XRANGEPLAIN]}$`);m("XRANGELOOSE",`^${c[u.GTLT]}\\s*${c[u.XRANGEPLAINLOOSE]}$`);m("COERCEPLAIN",`${"(^|[^\\d])"+"(\\d{1,"}${n}})`+`(?:\\.(\\d{1,${n}}))?`+`(?:\\.(\\d{1,${n}}))?`);m("COERCE",`${c[u.COERCEPLAIN]}(?:$|[^\\d])`);m("COERCEFULL",c[u.COERCEPLAIN]+`(?:${c[u.PRERELEASE]})?`+`(?:${c[u.BUILD]})?`+`(?:$|[^\\d])`);m("COERCERTL",c[u.COERCE],true);m("COERCERTLFULL",c[u.COERCEFULL],true);m("LONETILDE","(?:~>?)");m("TILDETRIM",`(\\s*)${c[u.LONETILDE]}\\s+`,true);t.tildeTrimReplace="$1~";m("TILDE",`^${c[u.LONETILDE]}${c[u.XRANGEPLAIN]}$`);m("TILDELOOSE",`^${c[u.LONETILDE]}${c[u.XRANGEPLAINLOOSE]}$`);m("LONECARET","(?:\\^)");m("CARETTRIM",`(\\s*)${c[u.LONECARET]}\\s+`,true);t.caretTrimReplace="$1^";m("CARET",`^${c[u.LONECARET]}${c[u.XRANGEPLAIN]}$`);m("CARETLOOSE",`^${c[u.LONECARET]}${c[u.XRANGEPLAINLOOSE]}$`);m("COMPARATORLOOSE",`^${c[u.GTLT]}\\s*(${c[u.LOOSEPLAIN]})$|^$`);m("COMPARATOR",`^${c[u.GTLT]}\\s*(${c[u.FULLPLAIN]})$|^$`);m("COMPARATORTRIM",`(\\s*)${c[u.GTLT]}\\s*(${c[u.LOOSEPLAIN]}|${c[u.XRANGEPLAIN]})`,true);t.comparatorTrimReplace="$1$2$3";m("HYPHENRANGE",`^\\s*(${c[u.XRANGEPLAIN]})`+`\\s+-\\s+`+`(${c[u.XRANGEPLAIN]})`+`\\s*$`);m("HYPHENRANGELOOSE",`^\\s*(${c[u.XRANGEPLAINLOOSE]})`+`\\s+-\\s+`+`(${c[u.XRANGEPLAINLOOSE]})`+`\\s*$`);m("STAR","(<|>)?=?\\s*\\*");m("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");m("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},75571:(e,t,r)=>{const n=r(97075);const s=(e,t,r)=>n(e,t,">",r);e.exports=s},76780:(e,t,r)=>{const n=r(78311);const s=(e,t,r)=>{e=new n(e,r);t=new n(t,r);return e.intersects(t,r)};e.exports=s},5342:(e,t,r)=>{const n=r(97075);const s=(e,t,r)=>n(e,t,"<",r);e.exports=s},19628:(e,t,r)=>{const n=r(31527);const s=r(78311);const o=(e,t,r)=>{let o=null;let a=null;let i=null;try{i=new s(t,r)}catch(l){return null}e.forEach((e=>{if(i.test(e)){if(!o||a.compare(e)===-1){o=e;a=new n(o,r)}}}));return o};e.exports=o},270:(e,t,r)=>{const n=r(31527);const s=r(78311);const o=(e,t,r)=>{let o=null;let a=null;let i=null;try{i=new s(t,r)}catch(l){return null}e.forEach((e=>{if(i.test(e)){if(!o||a.compare(e)===1){o=e;a=new n(o,r)}}}));return o};e.exports=o},41261:(e,t,r)=>{const n=r(31527);const s=r(78311);const o=r(35580);const a=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r)){return r}r=new n("0.0.0-0");if(e.test(r)){return r}r=null;for(let s=0;s<e.set.length;++s){const t=e.set[s];let a=null;t.forEach((e=>{const t=new n(e.semver.version);switch(e.operator){case">":if(t.prerelease.length===0){t.patch++}else{t.prerelease.push(0)}t.raw=t.format();case"":case">=":if(!a||o(t,a)){a=t}break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}}));if(a&&(!r||o(r,a))){r=a}}if(r&&e.test(r)){return r}return null};e.exports=a},97075:(e,t,r)=>{const n=r(31527);const s=r(93904);const{ANY:o}=s;const a=r(78311);const i=r(97638);const l=r(35580);const c=r(7059);const u=r(25200);const p=r(54089);const f=(e,t,r,f)=>{e=new n(e,f);t=new a(t,f);let h,E,m,d,g;switch(r){case">":h=l;E=u;m=c;d=">";g=">=";break;case"<":h=c;E=p;m=l;d="<";g="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(i(e,t,f)){return false}for(let n=0;n<t.set.length;++n){const r=t.set[n];let a=null;let i=null;r.forEach((e=>{if(e.semver===o){e=new s(">=0.0.0")}a=a||e;i=i||e;if(h(e.semver,a.semver,f)){a=e}else if(m(e.semver,i.semver,f)){i=e}}));if(a.operator===d||a.operator===g){return false}if((!i.operator||i.operator===d)&&E(e,i.semver)){return false}else if(i.operator===g&&m(e,i.semver)){return false}}return true};e.exports=f},72525:(e,t,r)=>{const n=r(97638);const s=r(50560);e.exports=(e,t,r)=>{const o=[];let a=null;let i=null;const l=e.sort(((e,t)=>s(e,t,r)));for(const s of l){const e=n(s,t,r);if(e){i=s;if(!a){a=s}}else{if(i){o.push([a,i])}i=null;a=null}}if(a){o.push([a,null])}const c=[];for(const[n,s]of o){if(n===s){c.push(n)}else if(!s&&n===l[0]){c.push("*")}else if(!s){c.push(`>=${n}`)}else if(n===l[0]){c.push(`<=${s}`)}else{c.push(`${n} - ${s}`)}}const u=c.join(" || ");const p=typeof t.raw==="string"?t.raw:String(t);return u.length<p.length?u:t}},75032:(e,t,r)=>{const n=r(78311);const s=r(93904);const{ANY:o}=s;const a=r(97638);const i=r(50560);const l=(e,t,r={})=>{if(e===t){return true}e=new n(e,r);t=new n(t,r);let s=false;e:for(const n of e.set){for(const e of t.set){const t=p(n,e,r);s=s||t!==null;if(t){continue e}}if(s){return false}}return true};const c=[new s(">=0.0.0-0")];const u=[new s(">=0.0.0")];const p=(e,t,r)=>{if(e===t){return true}if(e.length===1&&e[0].semver===o){if(t.length===1&&t[0].semver===o){return true}else if(r.includePrerelease){e=c}else{e=u}}if(t.length===1&&t[0].semver===o){if(r.includePrerelease){return true}else{t=u}}const n=new Set;let s,l;for(const o of e){if(o.operator===">"||o.operator===">="){s=f(s,o,r)}else if(o.operator==="<"||o.operator==="<="){l=h(l,o,r)}else{n.add(o.semver)}}if(n.size>1){return null}let p;if(s&&l){p=i(s.semver,l.semver,r);if(p>0){return null}else if(p===0&&(s.operator!==">="||l.operator!=="<=")){return null}}for(const o of n){if(s&&!a(o,String(s),r)){return null}if(l&&!a(o,String(l),r)){return null}for(const e of t){if(!a(o,String(e),r)){return false}}return true}let E,m;let d,g;let v=l&&!r.includePrerelease&&l.semver.prerelease.length?l.semver:false;let L=s&&!r.includePrerelease&&s.semver.prerelease.length?s.semver:false;if(v&&v.prerelease.length===1&&l.operator==="<"&&v.prerelease[0]===0){v=false}for(const o of t){g=g||o.operator===">"||o.operator===">=";d=d||o.operator==="<"||o.operator==="<=";if(s){if(L){if(o.semver.prerelease&&o.semver.prerelease.length&&o.semver.major===L.major&&o.semver.minor===L.minor&&o.semver.patch===L.patch){L=false}}if(o.operator===">"||o.operator===">="){E=f(s,o,r);if(E===o&&E!==s){return false}}else if(s.operator===">="&&!a(s.semver,String(o),r)){return false}}if(l){if(v){if(o.semver.prerelease&&o.semver.prerelease.length&&o.semver.major===v.major&&o.semver.minor===v.minor&&o.semver.patch===v.patch){v=false}}if(o.operator==="<"||o.operator==="<="){m=h(l,o,r);if(m===o&&m!==l){return false}}else if(l.operator==="<="&&!a(l.semver,String(o),r)){return false}}if(!o.operator&&(l||s)&&p!==0){return false}}if(s&&d&&!l&&p!==0){return false}if(l&&g&&!s&&p!==0){return false}if(L||v){return false}return true};const f=(e,t,r)=>{if(!e){return t}const n=i(e.semver,t.semver,r);return n>0?e:n<0?t:t.operator===">"&&e.operator===">="?t:e};const h=(e,t,r)=>{if(!e){return t}const n=i(e.semver,t.semver,r);return n<0?e:n>0?t:t.operator==="<"&&e.operator==="<="?t:e};e.exports=l},77631:(e,t,r)=>{const n=r(78311);const s=(e,t)=>new n(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")));e.exports=s},13874:(e,t,r)=>{const n=r(78311);const s=(e,t)=>{try{return new n(e,t).range||"*"}catch(r){return null}};e.exports=s}}]);