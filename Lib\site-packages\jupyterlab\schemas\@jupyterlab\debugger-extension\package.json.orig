{"name": "@jupyterlab/debugger-extension", "version": "4.3.6", "description": "JupyterLab - Debugger Extension", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.d.ts", "lib/**/*.js.map", "lib/**/*.js", "schema/*.json", "style/**/*.css", "style/**/*.svg", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo && rimraf tsconfig.test.tsbuildinfo && rimraf tests/build", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/cells": "^4.3.6", "@jupyterlab/codeeditor": "^4.3.6", "@jupyterlab/console": "^4.3.6", "@jupyterlab/coreutils": "^6.3.6", "@jupyterlab/debugger": "^4.3.6", "@jupyterlab/docregistry": "^4.3.6", "@jupyterlab/fileeditor": "^4.3.6", "@jupyterlab/logconsole": "^4.3.6", "@jupyterlab/notebook": "^4.3.6", "@jupyterlab/rendermime": "^4.3.6", "@jupyterlab/services": "^7.3.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/translation": "^4.3.6", "@lumino/commands": "^2.3.1"}, "devDependencies": {"@jupyterlab/testing": "^4.3.6", "@types/jest": "^29.2.0", "@types/react-dom": "^18.0.9", "rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}