{"name": "@jupyterlab/apputils-extension", "version": "4.3.6", "description": "JupyterLab - Application Utilities Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "style/*.css", "style/images/*.svg", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/coreutils": "^6.3.6", "@jupyterlab/docregistry": "^4.3.6", "@jupyterlab/mainmenu": "^4.3.6", "@jupyterlab/rendermime-interfaces": "^3.11.6", "@jupyterlab/services": "^7.3.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/statedb": "^4.3.6", "@jupyterlab/statusbar": "^4.3.6", "@jupyterlab/translation": "^4.3.6", "@jupyterlab/ui-components": "^4.3.6", "@jupyterlab/workspaces": "^4.3.6", "@lumino/algorithm": "^2.0.2", "@lumino/commands": "^2.3.1", "@lumino/coreutils": "^2.2.0", "@lumino/disposable": "^2.1.3", "@lumino/domutils": "^2.0.2", "@lumino/polling": "^2.1.3", "@lumino/widgets": "^2.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-toastify": "^9.0.8"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}