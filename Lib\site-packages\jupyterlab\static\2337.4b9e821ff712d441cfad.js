"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2337],{52337:(t,e,a)=>{a.d(e,{diagram:()=>ee});var s=a(76235);var r=a(1056);var n=a(40055);var i=a(16750);var o=a(74353);var c=a.n(o);var l=a(42838);var d=a.n(l);var h=function(){var t=function(t,e,a,s){for(a=a||{},s=t.length;s--;a[t[s]]=e);return a},e=[1,2],a=[1,3],s=[1,4],r=[2,4],n=[1,9],i=[1,11],o=[1,13],c=[1,14],l=[1,16],d=[1,17],h=[1,18],p=[1,24],u=[1,25],g=[1,26],f=[1,27],x=[1,28],y=[1,29],m=[1,30],b=[1,31],T=[1,32],E=[1,33],w=[1,34],v=[1,35],k=[1,36],_=[1,37],P=[1,38],L=[1,39],I=[1,41],M=[1,42],N=[1,43],A=[1,44],S=[1,45],O=[1,46],D=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],R=[4,5,16,50,52,53],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],Y=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,120];var q={trace:function t(){},yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,DOTTED_ARROW:74,SOLID_CROSS:75,DOTTED_CROSS:76,SOLID_POINT:77,DOTTED_POINT:78,TXT:79,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"DOTTED_ARROW",75:"SOLID_CROSS",76:"DOTTED_CROSS",77:"SOLID_POINT",78:"DOTTED_POINT",79:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:function t(e,a,s,r,n,i,o){var c=i.length-1;switch(n){case 3:r.apply(i[c]);return i[c];case 4:case 9:this.$=[];break;case 5:case 10:i[c-1].push(i[c]);this.$=i[c-1];break;case 6:case 7:case 11:case 12:this.$=i[c];break;case 8:case 13:this.$=[];break;case 15:i[c].type="createParticipant";this.$=i[c];break;case 16:i[c-1].unshift({type:"boxStart",boxData:r.parseBoxData(i[c-2])});i[c-1].push({type:"boxEnd",boxText:i[c-2]});this.$=i[c-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(i[c-2]),sequenceIndexStep:Number(i[c-1]),sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(i[c-1]),sequenceIndexStep:1,sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:false,signalType:r.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[c-1]};break;case 23:this.$={type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[c-1]};break;case 29:r.setDiagramTitle(i[c].substring(6));this.$=i[c].substring(6);break;case 30:r.setDiagramTitle(i[c].substring(7));this.$=i[c].substring(7);break;case 31:this.$=i[c].trim();r.setAccTitle(this.$);break;case 32:case 33:this.$=i[c].trim();r.setAccDescription(this.$);break;case 34:i[c-1].unshift({type:"loopStart",loopText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.LOOP_START});i[c-1].push({type:"loopEnd",loopText:i[c-2],signalType:r.LINETYPE.LOOP_END});this.$=i[c-1];break;case 35:i[c-1].unshift({type:"rectStart",color:r.parseMessage(i[c-2]),signalType:r.LINETYPE.RECT_START});i[c-1].push({type:"rectEnd",color:r.parseMessage(i[c-2]),signalType:r.LINETYPE.RECT_END});this.$=i[c-1];break;case 36:i[c-1].unshift({type:"optStart",optText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.OPT_START});i[c-1].push({type:"optEnd",optText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.OPT_END});this.$=i[c-1];break;case 37:i[c-1].unshift({type:"altStart",altText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.ALT_START});i[c-1].push({type:"altEnd",signalType:r.LINETYPE.ALT_END});this.$=i[c-1];break;case 38:i[c-1].unshift({type:"parStart",parText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.PAR_START});i[c-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END});this.$=i[c-1];break;case 39:i[c-1].unshift({type:"parStart",parText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.PAR_OVER_START});i[c-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END});this.$=i[c-1];break;case 40:i[c-1].unshift({type:"criticalStart",criticalText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.CRITICAL_START});i[c-1].push({type:"criticalEnd",signalType:r.LINETYPE.CRITICAL_END});this.$=i[c-1];break;case 41:i[c-1].unshift({type:"breakStart",breakText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.BREAK_START});i[c-1].push({type:"breakEnd",optText:r.parseMessage(i[c-2]),signalType:r.LINETYPE.BREAK_END});this.$=i[c-1];break;case 43:this.$=i[c-3].concat([{type:"option",optionText:r.parseMessage(i[c-1]),signalType:r.LINETYPE.CRITICAL_OPTION},i[c]]);break;case 45:this.$=i[c-3].concat([{type:"and",parText:r.parseMessage(i[c-1]),signalType:r.LINETYPE.PAR_AND},i[c]]);break;case 47:this.$=i[c-3].concat([{type:"else",altText:r.parseMessage(i[c-1]),signalType:r.LINETYPE.ALT_ELSE},i[c]]);break;case 48:i[c-3].draw="participant";i[c-3].type="addParticipant";i[c-3].description=r.parseMessage(i[c-1]);this.$=i[c-3];break;case 49:i[c-1].draw="participant";i[c-1].type="addParticipant";this.$=i[c-1];break;case 50:i[c-3].draw="actor";i[c-3].type="addParticipant";i[c-3].description=r.parseMessage(i[c-1]);this.$=i[c-3];break;case 51:i[c-1].draw="actor";i[c-1].type="addParticipant";this.$=i[c-1];break;case 52:i[c-1].type="destroyParticipant";this.$=i[c-1];break;case 53:this.$=[i[c-1],{type:"addNote",placement:i[c-2],actor:i[c-1].actor,text:i[c]}];break;case 54:i[c-2]=[].concat(i[c-1],i[c-1]).slice(0,2);i[c-2][0]=i[c-2][0].actor;i[c-2][1]=i[c-2][1].actor;this.$=[i[c-1],{type:"addNote",placement:r.PLACEMENT.OVER,actor:i[c-2].slice(0,2),text:i[c]}];break;case 55:this.$=[i[c-1],{type:"addLinks",actor:i[c-1].actor,text:i[c]}];break;case 56:this.$=[i[c-1],{type:"addALink",actor:i[c-1].actor,text:i[c]}];break;case 57:this.$=[i[c-1],{type:"addProperties",actor:i[c-1].actor,text:i[c]}];break;case 58:this.$=[i[c-1],{type:"addDetails",actor:i[c-1].actor,text:i[c]}];break;case 61:this.$=[i[c-2],i[c]];break;case 62:this.$=i[c];break;case 63:this.$=r.PLACEMENT.LEFTOF;break;case 64:this.$=r.PLACEMENT.RIGHTOF;break;case 65:this.$=[i[c-4],i[c-1],{type:"addMessage",from:i[c-4].actor,to:i[c-1].actor,signalType:i[c-3],msg:i[c],activate:true},{type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[c-1]}];break;case 66:this.$=[i[c-4],i[c-1],{type:"addMessage",from:i[c-4].actor,to:i[c-1].actor,signalType:i[c-3],msg:i[c]},{type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[c-4]}];break;case 67:this.$=[i[c-3],i[c-1],{type:"addMessage",from:i[c-3].actor,to:i[c-1].actor,signalType:i[c-2],msg:i[c]}];break;case 68:this.$={type:"addParticipant",actor:i[c]};break;case 69:this.$=r.LINETYPE.SOLID_OPEN;break;case 70:this.$=r.LINETYPE.DOTTED_OPEN;break;case 71:this.$=r.LINETYPE.SOLID;break;case 72:this.$=r.LINETYPE.DOTTED;break;case 73:this.$=r.LINETYPE.SOLID_CROSS;break;case 74:this.$=r.LINETYPE.DOTTED_CROSS;break;case 75:this.$=r.LINETYPE.SOLID_POINT;break;case 76:this.$=r.LINETYPE.DOTTED_POINT;break;case 77:this.$=r.parseMessage(i[c].trim().substring(1));break}},table:[{3:1,4:e,5:a,6:s},{1:[3]},{3:5,4:e,5:a,6:s},{3:6,4:e,5:a,6:s},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:n,5:i,8:8,9:10,12:12,13:o,14:c,17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},t(D,[2,5]),{9:47,12:12,13:o,14:c,17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},t(D,[2,7]),t(D,[2,8]),t(D,[2,14]),{12:48,50:_,52:P,53:L},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:O},{22:55,70:O},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(D,[2,29]),t(D,[2,30]),{32:[1,61]},{34:[1,62]},t(D,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:O},{22:72,70:O},{22:73,70:O},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82]},{55:83,57:[1,84],65:[1,85],66:[1,86]},{22:87,70:O},{22:88,70:O},{22:89,70:O},{22:90,70:O},t([5,51,64,71,72,73,74,75,76,77,78,79],[2,68]),t(D,[2,6]),t(D,[2,15]),t(R,[2,9],{10:91}),t(D,[2,17]),{5:[1,93],19:[1,92]},{5:[1,94]},t(D,[2,21]),{5:[1,95]},{5:[1,96]},t(D,[2,24]),t(D,[2,25]),t(D,[2,26]),t(D,[2,27]),t(D,[2,28]),t(D,[2,31]),t(D,[2,32]),t(C,r,{7:97}),t(C,r,{7:98}),t(C,r,{7:99}),t($,r,{40:100,7:101}),t(Y,r,{42:102,7:103}),t(Y,r,{7:103,42:104}),t(B,r,{45:105,7:106}),t(C,r,{7:107}),{5:[1,109],51:[1,108]},{5:[1,111],51:[1,110]},{5:[1,112]},{22:115,68:[1,113],69:[1,114],70:O},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),{22:116,70:O},{22:118,58:117,70:O},{70:[2,63]},{70:[2,64]},{56:119,79:F},{56:121,79:F},{56:122,79:F},{56:123,79:F},{4:[1,126],5:[1,128],11:125,12:127,16:[1,124],50:_,52:P,53:L},{5:[1,129]},t(D,[2,19]),t(D,[2,20]),t(D,[2,22]),t(D,[2,23]),{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[1,130],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[1,131],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[1,132],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{16:[1,133]},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[2,46],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,49:[1,134],50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{16:[1,135]},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[2,44],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,48:[1,136],50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{16:[1,137]},{16:[1,138]},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[2,42],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,47:[1,139],50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{4:n,5:i,8:8,9:10,12:12,13:o,14:c,16:[1,140],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:u,31:g,33:f,35:x,36:y,37:m,38:b,39:T,41:E,43:w,44:v,46:k,50:_,52:P,53:L,54:I,59:M,60:N,61:A,62:S,70:O},{15:[1,141]},t(D,[2,49]),{15:[1,142]},t(D,[2,51]),t(D,[2,52]),{22:143,70:O},{22:144,70:O},{56:145,79:F},{56:146,79:F},{56:147,79:F},{64:[1,148],79:[2,62]},{5:[2,55]},{5:[2,77]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(D,[2,16]),t(R,[2,10]),{12:149,50:_,52:P,53:L},t(R,[2,12]),t(R,[2,13]),t(D,[2,18]),t(D,[2,34]),t(D,[2,35]),t(D,[2,36]),t(D,[2,37]),{15:[1,150]},t(D,[2,38]),{15:[1,151]},t(D,[2,39]),t(D,[2,40]),{15:[1,152]},t(D,[2,41]),{5:[1,153]},{5:[1,154]},{56:155,79:F},{56:156,79:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:157,70:O},t(R,[2,11]),t($,r,{7:101,40:158}),t(Y,r,{7:103,42:159}),t(B,r,{7:106,45:160}),t(D,[2,48]),t(D,[2,50]),{5:[2,65]},{5:[2,66]},{79:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],85:[2,63],86:[2,64],119:[2,55],120:[2,77],121:[2,56],122:[2,57],123:[2,58],145:[2,67],146:[2,53],147:[2,54],155:[2,65],156:[2,66],157:[2,61],158:[2,47],159:[2,45],160:[2,43]},parseError:function t(e,a){if(a.recoverable){this.trace(e)}else{var s=new Error(e);s.hash=a;throw s}},parse:function t(e){var a=this,s=[0],r=[],n=[null],i=[],o=this.table,c="",l=0,d=0,h=2,p=1;var u=i.slice.call(arguments,1);var g=Object.create(this.lexer);var f={yy:{}};for(var x in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,x)){f.yy[x]=this.yy[x]}}g.setInput(e,f.yy);f.yy.lexer=g;f.yy.parser=this;if(typeof g.yylloc=="undefined"){g.yylloc={}}var y=g.yylloc;i.push(y);var m=g.options&&g.options.ranges;if(typeof f.yy.parseError==="function"){this.parseError=f.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function b(){var t;t=r.pop()||g.lex()||p;if(typeof t!=="number"){if(t instanceof Array){r=t;t=r.pop()}t=a.symbols_[t]||t}return t}var T,E,w,v,k={},_,P,L,I;while(true){E=s[s.length-1];if(this.defaultActions[E]){w=this.defaultActions[E]}else{if(T===null||typeof T=="undefined"){T=b()}w=o[E]&&o[E][T]}if(typeof w==="undefined"||!w.length||!w[0]){var M="";I=[];for(_ in o[E]){if(this.terminals_[_]&&_>h){I.push("'"+this.terminals_[_]+"'")}}if(g.showPosition){M="Parse error on line "+(l+1)+":\n"+g.showPosition()+"\nExpecting "+I.join(", ")+", got '"+(this.terminals_[T]||T)+"'"}else{M="Parse error on line "+(l+1)+": Unexpected "+(T==p?"end of input":"'"+(this.terminals_[T]||T)+"'")}this.parseError(M,{text:g.match,token:this.terminals_[T]||T,line:g.yylineno,loc:y,expected:I})}if(w[0]instanceof Array&&w.length>1){throw new Error("Parse Error: multiple actions possible at state: "+E+", token: "+T)}switch(w[0]){case 1:s.push(T);n.push(g.yytext);i.push(g.yylloc);s.push(w[1]);T=null;{d=g.yyleng;c=g.yytext;l=g.yylineno;y=g.yylloc}break;case 2:P=this.productions_[w[1]][1];k.$=n[n.length-P];k._$={first_line:i[i.length-(P||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(P||1)].first_column,last_column:i[i.length-1].last_column};if(m){k._$.range=[i[i.length-(P||1)].range[0],i[i.length-1].range[1]]}v=this.performAction.apply(k,[c,d,l,f.yy,w[1],n,i].concat(u));if(typeof v!=="undefined"){return v}if(P){s=s.slice(0,-1*P*2);n=n.slice(0,-1*P);i=i.slice(0,-1*P)}s.push(this.productions_[w[1]][0]);n.push(k.$);i.push(k._$);L=o[s[s.length-2]][s[s.length-1]];s.push(L);break;case 3:return true}}return true}};var W=function(){var t={EOF:1,parseError:function t(e,a){if(this.yy.parser){this.yy.parser.parseError(e,a)}else{throw new Error(e)}},setInput:function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var e=t.length;var a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(a.length-1){this.yylineno-=a.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===s.length?this.yylloc.first_column:0)+s[s.length-a.length].length-a[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var a,s,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}s=t[0].match(/(?:\r\n?|\n).*/g);if(s){this.yylineno+=s.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(a){return a}else if(this._backtrack){for(var n in r){this[n]=r[n]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,a,s;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var n=0;n<r.length;n++){a=this._input.match(this.rules[r[n]]);if(a&&(!e||a[0].length>e[0].length)){e=a;s=n;if(this.options.backtrack_lexer){t=this.test_match(a,r[n]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[s]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var e=this.next();if(e){return e}else{return this.lex()}},begin:function t(e){this.conditionStack.push(e)},popState:function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}},pushState:function t(e){this.begin(e)},stateStackSize:function t(){return this.conditionStack.length},options:{"case-insensitive":true},performAction:function t(e,a,s,r){switch(s){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:this.begin("LINE");return 14;case 8:this.begin("ID");return 50;case 9:this.begin("ID");return 52;case 10:return 13;case 11:this.begin("ID");return 53;case 12:a.yytext=a.yytext.trim();this.begin("ALIAS");return 70;case 13:this.popState();this.popState();this.begin("LINE");return 51;case 14:this.popState();this.popState();return 5;case 15:this.begin("LINE");return 36;case 16:this.begin("LINE");return 37;case 17:this.begin("LINE");return 38;case 18:this.begin("LINE");return 39;case 19:this.begin("LINE");return 49;case 20:this.begin("LINE");return 41;case 21:this.begin("LINE");return 43;case 22:this.begin("LINE");return 48;case 23:this.begin("LINE");return 44;case 24:this.begin("LINE");return 47;case 25:this.begin("LINE");return 46;case 26:this.popState();return 15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:this.begin("ID");return 21;case 37:this.begin("ID");return 23;case 38:return 29;case 39:return 30;case 40:this.begin("acc_title");return 31;case 41:this.popState();return"acc_title_value";case 42:this.begin("acc_descr");return 33;case 43:this.popState();return"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:a.yytext=a.yytext.trim();return 70;case 53:return 73;case 54:return 74;case 55:return 71;case 56:return 72;case 57:return 75;case 58:return 76;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 68;case 63:return 69;case 64:return 5;case 65:return"INVALID"}},rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\->:\n,;]+?([\-]*[^\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:false},acc_descr:{rules:[43],inclusive:false},acc_title:{rules:[41],inclusive:false},ID:{rules:[2,3,12],inclusive:false},ALIAS:{rules:[2,3,13,14],inclusive:false},LINE:{rules:[2,3,26],inclusive:false},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65],inclusive:true}}};return t}();q.lexer=W;function z(){this.yy={}}z.prototype=q;q.Parser=z;return new z}();h.parser=h;const p=h;class u{constructor(t){this.init=t;this.records=this.init()}reset(){this.records=this.init()}}const g=new u((()=>({prevActor:void 0,actors:{},createdActors:{},destroyedActors:{},boxes:[],messages:[],notes:[],sequenceNumbersEnabled:false,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})));const f=function(t){g.records.boxes.push({name:t.text,wrap:t.wrap===void 0&&O()||!!t.wrap,fill:t.color,actorKeys:[]});g.records.currentBox=g.records.boxes.slice(-1)[0]};const x=function(t,e,a,s){let r=g.records.currentBox;const n=g.records.actors[t];if(n){if(g.records.currentBox&&n.box&&g.records.currentBox!==n.box){throw new Error("A same participant should only be defined in one Box: "+n.name+" can't be in '"+n.box.name+"' and in '"+g.records.currentBox.name+"' at the same time.")}r=n.box?n.box:g.records.currentBox;n.box=r;if(n&&e===n.name&&a==null){return}}if(a==null||a.text==null){a={text:e,wrap:null,type:s}}if(s==null||a.text==null){a={text:e,wrap:null,type:s}}g.records.actors[t]={box:r,name:e,description:a.text,wrap:a.wrap===void 0&&O()||!!a.wrap,prevActor:g.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:s||"participant"};if(g.records.prevActor&&g.records.actors[g.records.prevActor]){g.records.actors[g.records.prevActor].nextActor=t}if(g.records.currentBox){g.records.currentBox.actorKeys.push(t)}g.records.prevActor=t};const y=t=>{let e;let a=0;for(e=0;e<g.records.messages.length;e++){if(g.records.messages[e].type===$.ACTIVE_START&&g.records.messages[e].from.actor===t){a++}if(g.records.messages[e].type===$.ACTIVE_END&&g.records.messages[e].from.actor===t){a--}}return a};const m=function(t,e,a,s){g.records.messages.push({from:t,to:e,message:a.text,wrap:a.wrap===void 0&&O()||!!a.wrap,answer:s})};const b=function(t,e,a={text:void 0,wrap:void 0},s,r=false){if(s===$.ACTIVE_END){const e=y(t.actor);if(e<1){let e=new Error("Trying to inactivate an inactive participant ("+t.actor+")");e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]};throw e}}g.records.messages.push({from:t,to:e,message:a.text,wrap:a.wrap===void 0&&O()||!!a.wrap,type:s,activate:r});return true};const T=function(){return g.records.boxes.length>0};const E=function(){return g.records.boxes.some((t=>t.name))};const w=function(){return g.records.messages};const v=function(){return g.records.boxes};const k=function(){return g.records.actors};const _=function(){return g.records.createdActors};const P=function(){return g.records.destroyedActors};const L=function(t){return g.records.actors[t]};const I=function(){return Object.keys(g.records.actors)};const M=function(){g.records.sequenceNumbersEnabled=true};const N=function(){g.records.sequenceNumbersEnabled=false};const A=()=>g.records.sequenceNumbersEnabled;const S=function(t){g.records.wrapEnabled=t};const O=()=>{if(g.records.wrapEnabled!==void 0){return g.records.wrapEnabled}return(0,s.c)().sequence.wrap};const D=function(){g.reset();(0,s.t)()};const R=function(t){const e=t.trim();const a={text:e.replace(/^:?(?:no)?wrap:/,"").trim(),wrap:e.match(/^:?wrap:/)!==null?true:e.match(/^:?nowrap:/)!==null?false:void 0};s.l.debug("parseMessage:",a);return a};const C=function(t){const e=t.match(/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/);let a=e!=null&&e[1]?e[1].trim():"transparent";let r=e!=null&&e[2]?e[2].trim():void 0;if(window&&window.CSS){if(!window.CSS.supports("color",a)){a="transparent";r=t.trim()}}else{const e=(new Option).style;e.color=a;if(e.color!==a){a="transparent";r=t.trim()}}return{color:a,text:r!==void 0?(0,s.d)(r.replace(/^:?(?:no)?wrap:/,""),(0,s.c)()):void 0,wrap:r!==void 0?r.match(/^:?wrap:/)!==null?true:r.match(/^:?nowrap:/)!==null?false:void 0:void 0}};const $={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32};const Y={FILLED:0,OPEN:1};const B={LEFTOF:0,RIGHTOF:1,OVER:2};const V=function(t,e,a){const s={actor:t,placement:e,message:a.text,wrap:a.wrap===void 0&&O()||!!a.wrap};const r=[].concat(t,t);g.records.notes.push(s);g.records.messages.push({from:r[0],to:r[1],message:a.text,wrap:a.wrap===void 0&&O()||!!a.wrap,type:$.NOTE,placement:e})};const F=function(t,e){const a=L(t);try{let t=(0,s.d)(e.text,(0,s.c)());t=t.replace(/&amp;/g,"&");t=t.replace(/&equals;/g,"=");const r=JSON.parse(t);W(a,r)}catch(r){s.l.error("error while parsing actor link text",r)}};const q=function(t,e){const a=L(t);try{const t={};let o=(0,s.d)(e.text,(0,s.c)());var r=o.indexOf("@");o=o.replace(/&amp;/g,"&");o=o.replace(/&equals;/g,"=");var n=o.slice(0,r-1).trim();var i=o.slice(r+1).trim();t[n]=i;W(a,t)}catch(o){s.l.error("error while parsing actor link text",o)}};function W(t,e){if(t.links==null){t.links=e}else{for(let a in e){t.links[a]=e[a]}}}const z=function(t,e){const a=L(t);try{let t=(0,s.d)(e.text,(0,s.c)());const r=JSON.parse(t);H(a,r)}catch(r){s.l.error("error while parsing actor properties text",r)}};function H(t,e){if(t.properties==null){t.properties=e}else{for(let a in e){t.properties[a]=e[a]}}}function U(){g.records.currentBox=void 0}const j=function(t,e){const a=L(t);const r=document.getElementById(e.text);try{const t=r.innerHTML;const e=JSON.parse(t);if(e["properties"]){H(a,e["properties"])}if(e["links"]){W(a,e["links"])}}catch(n){s.l.error("error while parsing actor details text",n)}};const K=function(t,e){if(t!==void 0&&t.properties!==void 0){return t.properties[e]}return void 0};const X=function(t){if(Array.isArray(t)){t.forEach((function(t){X(t)}))}else{switch(t.type){case"sequenceIndex":g.records.messages.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:false,type:t.signalType});break;case"addParticipant":x(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(g.records.actors[t.actor]){throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior")}g.records.lastCreated=t.actor;x(t.actor,t.actor,t.description,t.draw);g.records.createdActors[t.actor]=g.records.messages.length;break;case"destroyParticipant":g.records.lastDestroyed=t.actor;g.records.destroyedActors[t.actor]=g.records.messages.length;break;case"activeStart":b(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":b(t.actor,void 0,void 0,t.signalType);break;case"addNote":V(t.actor,t.placement,t.text);break;case"addLinks":F(t.actor,t.text);break;case"addALink":q(t.actor,t.text);break;case"addProperties":z(t.actor,t.text);break;case"addDetails":j(t.actor,t.text);break;case"addMessage":if(g.records.lastCreated){if(t.to!==g.records.lastCreated){throw new Error("The created participant "+g.records.lastCreated+" does not have an associated creating message after its declaration. Please check the sequence diagram.")}else{g.records.lastCreated=void 0}}else if(g.records.lastDestroyed){if(t.to!==g.records.lastDestroyed&&t.from!==g.records.lastDestroyed){throw new Error("The destroyed participant "+g.records.lastDestroyed+" does not have an associated destroying message after its declaration. Please check the sequence diagram.")}else{g.records.lastDestroyed=void 0}}b(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":f(t.boxData);break;case"boxEnd":U();break;case"loopStart":b(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":b(void 0,void 0,void 0,t.signalType);break;case"rectStart":b(void 0,void 0,t.color,t.signalType);break;case"rectEnd":b(void 0,void 0,void 0,t.signalType);break;case"optStart":b(void 0,void 0,t.optText,t.signalType);break;case"optEnd":b(void 0,void 0,void 0,t.signalType);break;case"altStart":b(void 0,void 0,t.altText,t.signalType);break;case"else":b(void 0,void 0,t.altText,t.signalType);break;case"altEnd":b(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":(0,s.s)(t.text);break;case"parStart":b(void 0,void 0,t.parText,t.signalType);break;case"and":b(void 0,void 0,t.parText,t.signalType);break;case"parEnd":b(void 0,void 0,void 0,t.signalType);break;case"criticalStart":b(void 0,void 0,t.criticalText,t.signalType);break;case"option":b(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":b(void 0,void 0,void 0,t.signalType);break;case"breakStart":b(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":b(void 0,void 0,void 0,t.signalType);break}}};const G={addActor:x,addMessage:m,addSignal:b,addLinks:F,addDetails:j,addProperties:z,autoWrap:O,setWrap:S,enableSequenceNumbers:M,disableSequenceNumbers:N,showSequenceNumbers:A,getMessages:w,getActors:k,getCreatedActors:_,getDestroyedActors:P,getActor:L,getActorKeys:I,getActorProperty:K,getAccTitle:s.g,getBoxes:v,getDiagramTitle:s.r,setDiagramTitle:s.q,getConfig:()=>(0,s.c)().sequence,clear:D,parseMessage:R,parseBoxData:C,LINETYPE:$,ARROWTYPE:Y,PLACEMENT:B,addNote:V,setAccTitle:s.s,apply:X,setAccDescription:s.b,getAccDescription:s.a,hasAtLeastOneBox:T,hasAtLeastOneBoxWithTitle:E};const J=t=>`.actor {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${t.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${t.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${t.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${t.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${t.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .messageText {\n    fill: ${t.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${t.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${t.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${t.noteBorderColor};\n    fill: ${t.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${t.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${t.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n    stroke-width: 2px;\n  }\n`;const Z=J;const Q=18*2;const tt=function(t,e){return(0,n.d)(t,e)};const et=(t,e)=>{(0,s.F)((()=>{const a=document.querySelectorAll(t);if(a.length===0){return}a[0].addEventListener("mouseover",(function(){nt("actor"+e+"_popup")}));a[0].addEventListener("mouseout",(function(){it("actor"+e+"_popup")}))}))};const at=function(t,e,a,s,r){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0){return{height:0,width:0}}const n=e.links;const o=e.actorCnt;const c=e.rectData;var l="none";if(r){l="block !important"}const d=t.append("g");d.attr("id","actor"+o+"_popup");d.attr("class","actorPopupMenu");d.attr("display",l);et("#actor"+o+"_popup",o);var h="";if(c.class!==void 0){h=" "+c.class}let p=c.width>a?c.width:a;const u=d.append("rect");u.attr("class","actorPopupMenuPanel"+h);u.attr("x",c.x);u.attr("y",c.height);u.attr("fill",c.fill);u.attr("stroke",c.stroke);u.attr("width",p);u.attr("height",c.height);u.attr("rx",c.rx);u.attr("ry",c.ry);if(n!=null){var g=20;for(let t in n){var f=d.append("a");var x=(0,i.Jf)(n[t]);f.attr("xlink:href",x);f.attr("target","_blank");Mt(s)(t,f,c.x+10,c.height+g,p,20,{class:"actor"},s);g+=30}}u.attr("height",g);return{height:c.height+g,width:p}};const st=function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = 'block'; }"};const rt=function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = 'none'; }"};const nt=function(t){var e=document.getElementById(t);if(e!=null){e.style.display="block"}};const it=function(t){var e=document.getElementById(t);if(e!=null){e.style.display="none"}};const ot=function(t,e){let a=0;let r=0;const n=e.text.split(s.e.lineBreakRegex);const[i,o]=(0,s.C)(e.fontSize);let c=[];let l=0;let d=()=>e.y;if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0){switch(e.valign){case"top":case"start":d=()=>Math.round(e.y+e.textMargin);break;case"middle":case"center":d=()=>Math.round(e.y+(a+r+e.textMargin)/2);break;case"bottom":case"end":d=()=>Math.round(e.y+(a+r+2*e.textMargin)-e.textMargin);break}}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0){switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin);e.anchor="start";e.dominantBaseline="middle";e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2);e.anchor="middle";e.dominantBaseline="middle";e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin);e.anchor="end";e.dominantBaseline="middle";e.alignmentBaseline="middle";break}}for(let[h,p]of n.entries()){if(e.textMargin!==void 0&&e.textMargin===0&&i!==void 0){l=h*i}const n=t.append("text");n.attr("x",e.x);n.attr("y",d());if(e.anchor!==void 0){n.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline)}if(e.fontFamily!==void 0){n.style("font-family",e.fontFamily)}if(o!==void 0){n.style("font-size",o)}if(e.fontWeight!==void 0){n.style("font-weight",e.fontWeight)}if(e.fill!==void 0){n.attr("fill",e.fill)}if(e.class!==void 0){n.attr("class",e.class)}if(e.dy!==void 0){n.attr("dy",e.dy)}else if(l!==0){n.attr("dy",l)}const u=p||s.Z;if(e.tspan){const t=n.append("tspan");t.attr("x",e.x);if(e.fill!==void 0){t.attr("fill",e.fill)}t.text(u)}else{n.text(u)}if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0){r+=(n._groups||n)[0][0].getBBox().height;a=r}c.push(n)}return c};const ct=function(t,e){function a(t,e,a,s,r){return t+","+e+" "+(t+a)+","+e+" "+(t+a)+","+(e+s-r)+" "+(t+a-r*1.2)+","+(e+s)+" "+t+","+(e+s)}const s=t.append("polygon");s.attr("points",a(e.x,e.y,e.width,e.height,7));s.attr("class","labelBox");e.y=e.y+e.height/2;ot(t,e);return s};let lt=-1;const dt=(t,e,a,s)=>{if(!t.select){return}a.forEach((a=>{const r=e[a];const n=t.select("#actor"+r.actorCnt);if(!s.mirrorActors&&r.stopy){n.attr("y2",r.stopy+r.height/2)}else if(s.mirrorActors){n.attr("y2",r.stopy)}}))};const ht=function(t,e,a,s){const r=s?e.stopy:e.starty;const i=e.x+e.width/2;const o=r+5;const c=t.append("g").lower();var l=c;if(!s){lt++;l.append("line").attr("id","actor"+lt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999");l=c.append("g");e.actorCnt=lt;if(e.links!=null){l.attr("id","root-"+lt);et("#root-"+lt,lt)}}const d=(0,n.g)();var h="actor";if(e.properties!=null&&e.properties["class"]){h=e.properties["class"]}else{d.fill="#eaeaea"}d.x=e.x;d.y=r;d.width=e.width;d.height=e.height;d.class=h;d.rx=3;d.ry=3;const p=tt(l,d);e.rectData=d;if(e.properties!=null&&e.properties["icon"]){const t=e.properties["icon"].trim();if(t.charAt(0)==="@"){(0,n.b)(l,d.x+d.width-20,d.y+10,t.substr(1))}else{(0,n.c)(l,d.x+d.width-20,d.y+10,t)}}It(a)(e.description,l,d.x,d.y,d.width,d.height,{class:"actor"},a);let u=e.height;if(p.node){const t=p.node().getBBox();e.height=t.height;u=t.height}return u};const pt=function(t,e,a,s){const r=s?e.stopy:e.starty;const i=e.x+e.width/2;const o=r+80;t.lower();if(!s){lt++;t.append("line").attr("id","actor"+lt).attr("x1",i).attr("y1",o).attr("x2",i).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999");e.actorCnt=lt}const c=t.append("g");c.attr("class","actor-man");const l=(0,n.g)();l.x=e.x;l.y=r;l.fill="#eaeaea";l.width=e.width;l.height=e.height;l.class="actor";l.rx=3;l.ry=3;c.append("line").attr("id","actor-man-torso"+lt).attr("x1",i).attr("y1",r+25).attr("x2",i).attr("y2",r+45);c.append("line").attr("id","actor-man-arms"+lt).attr("x1",i-Q/2).attr("y1",r+33).attr("x2",i+Q/2).attr("y2",r+33);c.append("line").attr("x1",i-Q/2).attr("y1",r+60).attr("x2",i).attr("y2",r+45);c.append("line").attr("x1",i).attr("y1",r+45).attr("x2",i+Q/2-2).attr("y2",r+60);const d=c.append("circle");d.attr("cx",e.x+e.width/2);d.attr("cy",r+10);d.attr("r",15);d.attr("width",e.width);d.attr("height",e.height);const h=c.node().getBBox();e.height=h.height;It(a)(e.description,c,l.x,l.y+35,l.width,l.height,{class:"actor"},a);return e.height};const ut=function(t,e,a,s){switch(e.type){case"actor":return pt(t,e,a,s);case"participant":return ht(t,e,a,s)}};const gt=function(t,e,a){const s=t.append("g");const r=s;mt(r,e);if(e.name){It(a)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a)}r.lower()};const ft=function(t){return t.append("g")};const xt=function(t,e,a,s,r){const i=(0,n.g)();const o=e.anchored;i.x=e.startx;i.y=e.starty;i.class="activation"+r%3;i.width=e.stopx-e.startx;i.height=a-e.starty;tt(o,i)};const yt=function(t,e,a,s){const{boxMargin:r,boxTextMargin:i,labelBoxHeight:o,labelBoxWidth:c,messageFontFamily:l,messageFontSize:d,messageFontWeight:h}=s;const p=t.append("g");const u=function(t,e,a,s){return p.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",s).attr("class","loopLine")};u(e.startx,e.starty,e.stopx,e.starty);u(e.stopx,e.starty,e.stopx,e.stopy);u(e.startx,e.stopy,e.stopx,e.stopy);u(e.startx,e.starty,e.startx,e.stopy);if(e.sections!==void 0){e.sections.forEach((function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")}))}let g=(0,n.e)();g.text=a;g.x=e.startx;g.y=e.starty;g.fontFamily=l;g.fontSize=d;g.fontWeight=h;g.anchor="middle";g.valign="middle";g.tspan=false;g.width=c||50;g.height=o||20;g.textMargin=i;g.class="labelText";ct(p,g);g=Pt();g.text=e.title;g.x=e.startx+c/2+(e.stopx-e.startx)/2;g.y=e.starty+r+i;g.anchor="middle";g.valign="middle";g.textMargin=i;g.class="loopText";g.fontFamily=l;g.fontSize=d;g.fontWeight=h;g.wrap=true;let f=ot(p,g);if(e.sectionTitles!==void 0){e.sectionTitles.forEach((function(t,a){if(t.message){g.text=t.message;g.x=e.startx+(e.stopx-e.startx)/2;g.y=e.sections[a].y+r+i;g.class="loopText";g.anchor="middle";g.valign="middle";g.tspan=false;g.fontFamily=l;g.fontSize=d;g.fontWeight=h;g.wrap=e.wrap;f=ot(p,g);let s=Math.round(f.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));e.sections[a].height+=s-(r+i)}}))}e.height=Math.round(e.stopy-e.starty);return p};const mt=function(t,e){(0,n.a)(t,e)};const bt=function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")};const Tt=function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")};const Et=function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")};const wt=function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")};const vt=function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")};const kt=function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)};const _t=function(t){const e=t.append("defs");const a=e.append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5);a.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")};const Pt=function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:true,valign:void 0}};const Lt=function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}};const It=function(){function t(t,e,a,s,n,i,o){const c=e.append("text").attr("x",a+n/2).attr("y",s+i/2+5).style("text-anchor","middle").text(t);r(c,o)}function e(t,e,a,n,i,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l;const[u,g]=(0,s.C)(d);const f=t.split(s.e.lineBreakRegex);for(let s=0;s<f.length;s++){const t=s*u-u*(f.length-1)/2;const l=e.append("text").attr("x",a+i/2).attr("y",n).style("text-anchor","middle").style("font-size",g).style("font-weight",p).style("font-family",h);l.append("tspan").attr("x",a+i/2).attr("dy",t).text(f[s]);l.attr("y",n+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central");r(l,c)}}function a(t,a,s,n,i,o,c,l){const d=a.append("switch");const h=d.append("foreignObject").attr("x",s).attr("y",n).attr("width",i).attr("height",o);const p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,d,s,n,i,o,c,l);r(p,c)}function r(t,e){for(const a in e){if(e.hasOwnProperty(a)){t.attr(a,e[a])}}}return function(s){return s.textPlacement==="fo"?a:s.textPlacement==="old"?t:e}}();const Mt=function(){function t(t,e,a,s,n,i,o){const c=e.append("text").attr("x",a).attr("y",s).style("text-anchor","start").text(t);r(c,o)}function e(t,e,a,n,i,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l;const u=t.split(s.e.lineBreakRegex);for(let s=0;s<u.length;s++){const t=s*d-d*(u.length-1)/2;const i=e.append("text").attr("x",a).attr("y",n).style("text-anchor","start").style("font-size",d).style("font-weight",p).style("font-family",h);i.append("tspan").attr("x",a).attr("dy",t).text(u[s]);i.attr("y",n+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central");r(i,c)}}function a(t,a,s,n,i,o,c,l){const d=a.append("switch");const h=d.append("foreignObject").attr("x",s).attr("y",n).attr("width",i).attr("height",o);const p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,d,s,n,i,o,c,l);r(p,c)}function r(t,e){for(const a in e){if(e.hasOwnProperty(a)){t.attr(a,e[a])}}}return function(s){return s.textPlacement==="fo"?a:s.textPlacement==="old"?t:e}}();const Nt={drawRect:tt,drawText:ot,drawLabel:ct,drawActor:ut,drawBox:gt,drawPopup:at,anchorElement:ft,drawActivation:xt,drawLoop:yt,drawBackgroundRect:mt,insertArrowHead:wt,insertArrowFilledHead:vt,insertSequenceNumber:kt,insertArrowCrossHead:_t,insertDatabaseIcon:bt,insertComputerIcon:Tt,insertClockIcon:Et,getTextObj:Pt,getNoteRect:Lt,popupMenu:st,popdownMenu:rt,fixLifeLineHeights:dt,sanitizeUrl:i.Jf};let At={};const St={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map((t=>t.height||0)))+(this.loops.length===0?0:this.loops.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(this.messages.length===0?0:this.messages.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(this.notes.length===0?0:this.notes.map((t=>t.height||0)).reduce(((t,e)=>t+e)))},clear:function(){this.actors=[];this.boxes=[];this.loops=[];this.messages=[];this.notes=[]},addBox:function(t){this.boxes.push(t)},addActor:function(t){this.actors.push(t)},addLoop:function(t){this.loops.push(t)},addMessage:function(t){this.messages.push(t)},addNote:function(t){this.notes.push(t)},lastActor:function(){return this.actors[this.actors.length-1]},lastLoop:function(){return this.loops[this.loops.length-1]},lastMessage:function(){return this.messages[this.messages.length-1]},lastNote:function(){return this.notes[this.notes.length-1]},actors:[],boxes:[],loops:[],messages:[],notes:[]},init:function(){this.sequenceItems=[];this.activations=[];this.models.clear();this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0};this.verticalPos=0;qt((0,s.c)())},updateVal:function(t,e,a,s){if(t[e]===void 0){t[e]=a}else{t[e]=s(a,t[e])}},updateBounds:function(t,e,a,s){const r=this;let n=0;function i(i){return function o(c){n++;const l=r.sequenceItems.length-n+1;r.updateVal(c,"starty",e-l*At.boxMargin,Math.min);r.updateVal(c,"stopy",s+l*At.boxMargin,Math.max);r.updateVal(St.data,"startx",t-l*At.boxMargin,Math.min);r.updateVal(St.data,"stopx",a+l*At.boxMargin,Math.max);if(!(i==="activation")){r.updateVal(c,"startx",t-l*At.boxMargin,Math.min);r.updateVal(c,"stopx",a+l*At.boxMargin,Math.max);r.updateVal(St.data,"starty",e-l*At.boxMargin,Math.min);r.updateVal(St.data,"stopy",s+l*At.boxMargin,Math.max)}}}this.sequenceItems.forEach(i());this.activations.forEach(i("activation"))},insert:function(t,e,a,r){const n=s.e.getMin(t,a);const i=s.e.getMax(t,a);const o=s.e.getMin(e,r);const c=s.e.getMax(e,r);this.updateVal(St.data,"startx",n,Math.min);this.updateVal(St.data,"starty",o,Math.min);this.updateVal(St.data,"stopx",i,Math.max);this.updateVal(St.data,"stopy",c,Math.max);this.updateBounds(n,o,i,c)},newActivation:function(t,e,a){const s=a[t.from.actor];const r=Wt(t.from.actor).length||0;const n=s.x+s.width/2+(r-1)*At.activationWidth/2;this.activations.push({startx:n,starty:this.verticalPos+2,stopx:n+At.activationWidth,stopy:void 0,actor:t.from.actor,anchored:Nt.anchorElement(e)})},endActivation:function(t){const e=this.activations.map((function(t){return t.actor})).lastIndexOf(t.from.actor);return this.activations.splice(e,1)[0]},createLoop:function(t={message:void 0,wrap:false,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},newLoop:function(t={message:void 0,wrap:false,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},endLoop:function(){return this.sequenceItems.pop()},isLoopOverlap:function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:false},addSectionToLoop:function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[];e.sectionTitles=e.sectionTitles||[];e.sections.push({y:St.getVerticalPos(),height:0});e.sectionTitles.push(t);this.sequenceItems.push(e)},saveVerticalPos:function(){if(this.isLoopOverlap()){this.savedVerticalPos=this.verticalPos}},resetVerticalPos:function(){if(this.isLoopOverlap()){this.verticalPos=this.savedVerticalPos}},bumpVerticalPos:function(t){this.verticalPos=this.verticalPos+t;this.data.stopy=s.e.getMax(this.data.stopy,this.verticalPos)},getVerticalPos:function(){return this.verticalPos},getBounds:function(){return{bounds:this.data,models:this.models}}};const Ot=function(t,e){St.bumpVerticalPos(At.boxMargin);e.height=At.boxMargin;e.starty=St.getVerticalPos();const a=(0,n.g)();a.x=e.startx;a.y=e.starty;a.width=e.width||At.width;a.class="note";const s=t.append("g");const r=Nt.drawRect(s,a);const i=(0,n.e)();i.x=e.startx;i.y=e.starty;i.width=a.width;i.dy="1em";i.text=e.message;i.class="noteText";i.fontFamily=At.noteFontFamily;i.fontSize=At.noteFontSize;i.fontWeight=At.noteFontWeight;i.anchor=At.noteAlign;i.textMargin=At.noteMargin;i.valign="center";const o=ot(s,i);const c=Math.round(o.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));r.attr("height",c+2*At.noteMargin);e.height+=c+2*At.noteMargin;St.bumpVerticalPos(c+2*At.noteMargin);e.stopy=e.starty+c+2*At.noteMargin;e.stopx=e.startx+a.width;St.insert(e.startx,e.starty,e.stopx,e.stopy);St.models.addNote(e)};const Dt=t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight});const Rt=t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight});const Ct=t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight});function $t(t,e){St.bumpVerticalPos(10);const{startx:a,stopx:r,message:n}=e;const i=s.e.splitBreaks(n).length;const o=s.u.calculateTextDimensions(n,Dt(At));const c=o.height/i;e.height+=c;St.bumpVerticalPos(c);let l;let d=o.height-10;const h=o.width;if(a===r){l=St.getVerticalPos()+d;if(!At.rightAngles){d+=At.boxMargin;l=St.getVerticalPos()+d}d+=30;const t=s.e.getMax(h/2,At.width/2);St.insert(a-t,St.getVerticalPos()-10+d,r+t,St.getVerticalPos()+30+d)}else{d+=At.boxMargin;l=St.getVerticalPos()+d;St.insert(a,l-10,r,l)}St.bumpVerticalPos(d);e.height+=d;e.stopy=e.starty+e.height;St.insert(e.fromBounds,e.starty,e.toBounds,e.stopy);return l}const Yt=function(t,e,a,r){const{startx:i,stopx:o,starty:c,message:l,type:d,sequenceIndex:h,sequenceVisible:p}=e;const u=s.u.calculateTextDimensions(l,Dt(At));const g=(0,n.e)();g.x=i;g.y=c+10;g.width=o-i;g.class="messageText";g.dy="1em";g.text=l;g.fontFamily=At.messageFontFamily;g.fontSize=At.messageFontSize;g.fontWeight=At.messageFontWeight;g.anchor=At.messageAlign;g.valign="center";g.textMargin=At.wrapPadding;g.tspan=false;ot(t,g);const f=u.width;let x;if(i===o){if(At.rightAngles){x=t.append("path").attr("d",`M  ${i},${a} H ${i+s.e.getMax(At.width/2,f/2)} V ${a+25} H ${i}`)}else{x=t.append("path").attr("d","M "+i+","+a+" C "+(i+60)+","+(a-10)+" "+(i+60)+","+(a+30)+" "+i+","+(a+20))}}else{x=t.append("line");x.attr("x1",i);x.attr("y1",a);x.attr("x2",o);x.attr("y2",a)}if(d===r.db.LINETYPE.DOTTED||d===r.db.LINETYPE.DOTTED_CROSS||d===r.db.LINETYPE.DOTTED_POINT||d===r.db.LINETYPE.DOTTED_OPEN){x.style("stroke-dasharray","3, 3");x.attr("class","messageLine1")}else{x.attr("class","messageLine0")}let y="";if(At.arrowMarkerAbsolute){y=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search;y=y.replace(/\(/g,"\\(");y=y.replace(/\)/g,"\\)")}x.attr("stroke-width",2);x.attr("stroke","none");x.style("fill","none");if(d===r.db.LINETYPE.SOLID||d===r.db.LINETYPE.DOTTED){x.attr("marker-end","url("+y+"#arrowhead)")}if(d===r.db.LINETYPE.SOLID_POINT||d===r.db.LINETYPE.DOTTED_POINT){x.attr("marker-end","url("+y+"#filled-head)")}if(d===r.db.LINETYPE.SOLID_CROSS||d===r.db.LINETYPE.DOTTED_CROSS){x.attr("marker-end","url("+y+"#crosshead)")}if(p||At.showSequenceNumbers){x.attr("marker-start","url("+y+"#sequencenumber)");t.append("text").attr("x",i).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(h)}};const Bt=function(t,e,a,r,n,i,o){let c=0;let l=0;let d=void 0;let h=0;for(const p of r){const t=e[p];const r=t.box;if(d&&d!=r){if(!o){St.models.addBox(d)}l+=At.boxMargin+d.margin}if(r&&r!=d){if(!o){r.x=c+l;r.y=n}l+=r.margin}t.width=t.width||At.width;t.height=s.e.getMax(t.height||At.height,At.height);t.margin=t.margin||At.actorMargin;h=s.e.getMax(h,t.height);if(a[t.name]){l+=t.width/2}t.x=c+l;t.starty=St.getVerticalPos();St.insert(t.x,n,t.x+t.width,t.height);c+=t.width+l;if(t.box){t.box.width=c+r.margin-t.box.x}l=t.margin;d=t.box;St.models.addActor(t)}if(d&&!o){St.models.addBox(d)}St.bumpVerticalPos(h)};const Vt=function(t,e,a,r){if(!r){for(const s of a){const a=e[s];Nt.drawActor(t,a,At,false)}}else{let r=0;St.bumpVerticalPos(At.boxMargin*2);for(const n of a){const a=e[n];if(!a.stopy){a.stopy=St.getVerticalPos()}const i=Nt.drawActor(t,a,At,true);r=s.e.getMax(r,i)}St.bumpVerticalPos(r+At.boxMargin)}};const Ft=function(t,e,a,s){let r=0;let n=0;for(const i of a){const a=e[i];const o=Xt(a);const c=Nt.drawPopup(t,a,o,At,At.forceMenus,s);if(c.height>r){r=c.height}if(c.width+a.x>n){n=c.width+a.x}}return{maxHeight:r,maxWidth:n}};const qt=function(t){(0,s.f)(At,t);if(t.fontFamily){At.actorFontFamily=At.noteFontFamily=At.messageFontFamily=t.fontFamily}if(t.fontSize){At.actorFontSize=At.noteFontSize=At.messageFontSize=t.fontSize}if(t.fontWeight){At.actorFontWeight=At.noteFontWeight=At.messageFontWeight=t.fontWeight}};const Wt=function(t){return St.activations.filter((function(e){return e.actor===t}))};const zt=function(t,e){const a=e[t];const r=Wt(t);const n=r.reduce((function(t,e){return s.e.getMin(t,e.startx)}),a.x+a.width/2-1);const i=r.reduce((function(t,e){return s.e.getMax(t,e.stopx)}),a.x+a.width/2+1);return[n,i]};function Ht(t,e,a,r,n){St.bumpVerticalPos(a);let i=r;if(e.id&&e.message&&t[e.id]){const a=t[e.id].width;const n=Dt(At);e.message=s.u.wrapLabel(`[${e.message}]`,a-2*At.wrapPadding,n);e.width=a;e.wrap=true;const o=s.u.calculateTextDimensions(e.message,n);const c=s.e.getMax(o.height,At.labelBoxHeight);i=r+c;s.l.debug(`${c} - ${e.message}`)}n(e);St.bumpVerticalPos(i)}function Ut(t,e,a,s,r,n,i){function o(a,s){if(a.x<r[t.from].x){St.insert(e.stopx-s,e.starty,e.startx,e.stopy+a.height/2+At.noteMargin);e.stopx=e.stopx+s}else{St.insert(e.startx,e.starty,e.stopx+s,e.stopy+a.height/2+At.noteMargin);e.stopx=e.stopx-s}}function c(a,s){if(a.x<r[t.to].x){St.insert(e.startx-s,e.starty,e.stopx,e.stopy+a.height/2+At.noteMargin);e.startx=e.startx+s}else{St.insert(e.stopx,e.starty,e.startx+s,e.stopy+a.height/2+At.noteMargin);e.startx=e.startx-s}}if(n[t.to]==s){const e=r[t.to];const s=e.type=="actor"?Q/2+3:e.width/2+3;o(e,s);e.starty=a-e.height/2;St.bumpVerticalPos(e.height/2)}else if(i[t.from]==s){const e=r[t.from];if(At.mirrorActors){const t=e.type=="actor"?Q/2:e.width/2;c(e,t)}e.stopy=a-e.height/2;St.bumpVerticalPos(e.height/2)}else if(i[t.to]==s){const e=r[t.to];if(At.mirrorActors){const t=e.type=="actor"?Q/2+3:e.width/2+3;o(e,t)}e.stopy=a-e.height/2;St.bumpVerticalPos(e.height/2)}}const jt=function(t,e,a,n){const{securityLevel:i,sequence:o}=(0,s.c)();At=o;let c;if(i==="sandbox"){c=(0,r.Ltv)("#i"+e)}const l=i==="sandbox"?(0,r.Ltv)(c.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const d=i==="sandbox"?c.nodes()[0].contentDocument:document;St.init();s.l.debug(n.db);const h=i==="sandbox"?l.select(`[id="${e}"]`):(0,r.Ltv)(`[id="${e}"]`);const p=n.db.getActors();const u=n.db.getCreatedActors();const g=n.db.getDestroyedActors();const f=n.db.getBoxes();let x=n.db.getActorKeys();const y=n.db.getMessages();const m=n.db.getDiagramTitle();const b=n.db.hasAtLeastOneBox();const T=n.db.hasAtLeastOneBoxWithTitle();const E=Kt(p,y,n);At.height=Gt(p,E,f);Nt.insertComputerIcon(h);Nt.insertDatabaseIcon(h);Nt.insertClockIcon(h);if(b){St.bumpVerticalPos(At.boxMargin);if(T){St.bumpVerticalPos(f[0].textMaxHeight)}}if(At.hideUnusedParticipants===true){const t=new Set;y.forEach((e=>{t.add(e.from);t.add(e.to)}));x=x.filter((e=>t.has(e)))}Bt(h,p,u,x,0,y,false);const w=Qt(y,p,E,n);Nt.insertArrowHead(h);Nt.insertArrowCrossHead(h);Nt.insertArrowFilledHead(h);Nt.insertSequenceNumber(h);function v(t,e){const a=St.endActivation(t);if(a.starty+18>e){a.starty=e-6;e+=12}Nt.drawActivation(h,a,e,At,Wt(t.from.actor).length);St.insert(a.startx,e-10,a.stopx,e)}let k=1;let _=1;const P=[];const L=[];y.forEach((function(t,e){let a,r,i;switch(t.type){case n.db.LINETYPE.NOTE:St.resetVerticalPos();r=t.noteModel;Ot(h,r);break;case n.db.LINETYPE.ACTIVE_START:St.newActivation(t,h,p);break;case n.db.LINETYPE.ACTIVE_END:v(t,St.getVerticalPos());break;case n.db.LINETYPE.LOOP_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));break;case n.db.LINETYPE.LOOP_END:a=St.endLoop();Nt.drawLoop(h,a,"loop",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;case n.db.LINETYPE.RECT_START:Ht(w,t,At.boxMargin,At.boxMargin,(t=>St.newLoop(void 0,t.message)));break;case n.db.LINETYPE.RECT_END:a=St.endLoop();L.push(a);St.models.addLoop(a);St.bumpVerticalPos(a.stopy-St.getVerticalPos());break;case n.db.LINETYPE.OPT_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));break;case n.db.LINETYPE.OPT_END:a=St.endLoop();Nt.drawLoop(h,a,"opt",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;case n.db.LINETYPE.ALT_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));break;case n.db.LINETYPE.ALT_ELSE:Ht(w,t,At.boxMargin+At.boxTextMargin,At.boxMargin,(t=>St.addSectionToLoop(t)));break;case n.db.LINETYPE.ALT_END:a=St.endLoop();Nt.drawLoop(h,a,"alt",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;case n.db.LINETYPE.PAR_START:case n.db.LINETYPE.PAR_OVER_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));St.saveVerticalPos();break;case n.db.LINETYPE.PAR_AND:Ht(w,t,At.boxMargin+At.boxTextMargin,At.boxMargin,(t=>St.addSectionToLoop(t)));break;case n.db.LINETYPE.PAR_END:a=St.endLoop();Nt.drawLoop(h,a,"par",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;case n.db.LINETYPE.AUTONUMBER:k=t.message.start||k;_=t.message.step||_;if(t.message.visible){n.db.enableSequenceNumbers()}else{n.db.disableSequenceNumbers()}break;case n.db.LINETYPE.CRITICAL_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));break;case n.db.LINETYPE.CRITICAL_OPTION:Ht(w,t,At.boxMargin+At.boxTextMargin,At.boxMargin,(t=>St.addSectionToLoop(t)));break;case n.db.LINETYPE.CRITICAL_END:a=St.endLoop();Nt.drawLoop(h,a,"critical",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;case n.db.LINETYPE.BREAK_START:Ht(w,t,At.boxMargin,At.boxMargin+At.boxTextMargin,(t=>St.newLoop(t)));break;case n.db.LINETYPE.BREAK_END:a=St.endLoop();Nt.drawLoop(h,a,"break",At);St.bumpVerticalPos(a.stopy-St.getVerticalPos());St.models.addLoop(a);break;default:try{i=t.msgModel;i.starty=St.getVerticalPos();i.sequenceIndex=k;i.sequenceVisible=n.db.showSequenceNumbers();const a=$t(h,i);Ut(t,i,a,e,p,u,g);P.push({messageModel:i,lineStartY:a});St.models.addMessage(i)}catch(o){s.l.error("error while drawing message",o)}}if([n.db.LINETYPE.SOLID_OPEN,n.db.LINETYPE.DOTTED_OPEN,n.db.LINETYPE.SOLID,n.db.LINETYPE.DOTTED,n.db.LINETYPE.SOLID_CROSS,n.db.LINETYPE.DOTTED_CROSS,n.db.LINETYPE.SOLID_POINT,n.db.LINETYPE.DOTTED_POINT].includes(t.type)){k=k+_}}));s.l.debug("createdActors",u);s.l.debug("destroyedActors",g);Vt(h,p,x,false);P.forEach((t=>Yt(h,t.messageModel,t.lineStartY,n)));if(At.mirrorActors){Vt(h,p,x,true)}L.forEach((t=>Nt.drawBackgroundRect(h,t)));dt(h,p,x,At);St.models.boxes.forEach((function(t){t.height=St.getVerticalPos()-t.y;St.insert(t.x,t.y,t.x+t.width,t.height);t.startx=t.x;t.starty=t.y;t.stopx=t.startx+t.width;t.stopy=t.starty+t.height;t.stroke="rgb(0,0,0, 0.5)";Nt.drawBox(h,t,At)}));if(b){St.bumpVerticalPos(At.boxMargin)}const I=Ft(h,p,x,d);const{bounds:M}=St.getBounds();let N=M.stopy-M.starty;if(N<I.maxHeight){N=I.maxHeight}let A=N+2*At.diagramMarginY;if(At.mirrorActors){A=A-At.boxMargin+At.bottomMarginAdj}let S=M.stopx-M.startx;if(S<I.maxWidth){S=I.maxWidth}const O=S+2*At.diagramMarginX;if(m){h.append("text").text(m).attr("x",(M.stopx-M.startx)/2-2*At.diagramMarginX).attr("y",-25)}(0,s.i)(h,A,O,At.useMaxWidth);const D=m?40:0;h.attr("viewBox",M.startx-At.diagramMarginX+" -"+(At.diagramMarginY+D)+" "+O+" "+(A+D));s.l.debug(`models:`,St.models)};function Kt(t,e,a){const r={};e.forEach((function(e){if(t[e.to]&&t[e.from]){const n=t[e.to];if(e.placement===a.db.PLACEMENT.LEFTOF&&!n.prevActor){return}if(e.placement===a.db.PLACEMENT.RIGHTOF&&!n.nextActor){return}const i=e.placement!==void 0;const o=!i;const c=i?Rt(At):Dt(At);const l=e.wrap?s.u.wrapLabel(e.message,At.width-2*At.wrapPadding,c):e.message;const d=s.u.calculateTextDimensions(l,c);const h=d.width+2*At.wrapPadding;if(o&&e.from===n.nextActor){r[e.to]=s.e.getMax(r[e.to]||0,h)}else if(o&&e.from===n.prevActor){r[e.from]=s.e.getMax(r[e.from]||0,h)}else if(o&&e.from===e.to){r[e.from]=s.e.getMax(r[e.from]||0,h/2);r[e.to]=s.e.getMax(r[e.to]||0,h/2)}else if(e.placement===a.db.PLACEMENT.RIGHTOF){r[e.from]=s.e.getMax(r[e.from]||0,h)}else if(e.placement===a.db.PLACEMENT.LEFTOF){r[n.prevActor]=s.e.getMax(r[n.prevActor]||0,h)}else if(e.placement===a.db.PLACEMENT.OVER){if(n.prevActor){r[n.prevActor]=s.e.getMax(r[n.prevActor]||0,h/2)}if(n.nextActor){r[e.from]=s.e.getMax(r[e.from]||0,h/2)}}}}));s.l.debug("maxMessageWidthPerActor:",r);return r}const Xt=function(t){let e=0;const a=Ct(At);for(const r in t.links){const t=s.u.calculateTextDimensions(r,a);const n=t.width+2*At.wrapPadding+2*At.boxMargin;if(e<n){e=n}}return e};function Gt(t,e,a){let r=0;Object.keys(t).forEach((e=>{const a=t[e];if(a.wrap){a.description=s.u.wrapLabel(a.description,At.width-2*At.wrapPadding,Ct(At))}const n=s.u.calculateTextDimensions(a.description,Ct(At));a.width=a.wrap?At.width:s.e.getMax(At.width,n.width+2*At.wrapPadding);a.height=a.wrap?s.e.getMax(n.height,At.height):At.height;r=s.e.getMax(r,a.height)}));for(const i in e){const a=t[i];if(!a){continue}const r=t[a.nextActor];if(!r){const t=e[i];const r=t+At.actorMargin-a.width/2;a.margin=s.e.getMax(r,At.actorMargin);continue}const n=e[i];const o=n+At.actorMargin-a.width/2-r.width/2;a.margin=s.e.getMax(o,At.actorMargin)}let n=0;a.forEach((e=>{const a=Dt(At);let r=e.actorKeys.reduce(((e,a)=>e+=t[a].width+(t[a].margin||0)),0);r-=2*At.boxTextMargin;if(e.wrap){e.name=s.u.wrapLabel(e.name,r-2*At.wrapPadding,a)}const i=s.u.calculateTextDimensions(e.name,a);n=s.e.getMax(i.height,n);const o=s.e.getMax(r,i.width+2*At.wrapPadding);e.margin=At.boxTextMargin;if(r<o){const t=(o-r)/2;e.margin+=t}}));a.forEach((t=>t.textMaxHeight=n));return s.e.getMax(r,At.height)}const Jt=function(t,e,a){const r=e[t.from].x;const n=e[t.to].x;const i=t.wrap&&t.message;let o=s.u.calculateTextDimensions(i?s.u.wrapLabel(t.message,At.width,Rt(At)):t.message,Rt(At));const c={width:i?At.width:s.e.getMax(At.width,o.width+2*At.noteMargin),height:0,startx:e[t.from].x,stopx:0,starty:0,stopy:0,message:t.message};if(t.placement===a.db.PLACEMENT.RIGHTOF){c.width=i?s.e.getMax(At.width,o.width):s.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*At.noteMargin);c.startx=r+(e[t.from].width+At.actorMargin)/2}else if(t.placement===a.db.PLACEMENT.LEFTOF){c.width=i?s.e.getMax(At.width,o.width+2*At.noteMargin):s.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*At.noteMargin);c.startx=r-c.width+(e[t.from].width-At.actorMargin)/2}else if(t.to===t.from){o=s.u.calculateTextDimensions(i?s.u.wrapLabel(t.message,s.e.getMax(At.width,e[t.from].width),Rt(At)):t.message,Rt(At));c.width=i?s.e.getMax(At.width,e[t.from].width):s.e.getMax(e[t.from].width,At.width,o.width+2*At.noteMargin);c.startx=r+(e[t.from].width-c.width)/2}else{c.width=Math.abs(r+e[t.from].width/2-(n+e[t.to].width/2))+At.actorMargin;c.startx=r<n?r+e[t.from].width/2-At.actorMargin/2:n+e[t.to].width/2-At.actorMargin/2}if(i){c.message=s.u.wrapLabel(t.message,c.width-2*At.wrapPadding,Rt(At))}s.l.debug(`NM:[${c.startx},${c.stopx},${c.starty},${c.stopy}:${c.width},${c.height}=${t.message}]`);return c};const Zt=function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT].includes(t.type)){return{}}const[r,n]=zt(t.from,e);const[i,o]=zt(t.to,e);const c=r<=i;const l=c?n:r;let d=c?i:o;const h=Math.abs(i-o)>2;const p=t=>c?-t:t;if(t.from===t.to){d=l}else{if(t.activate&&!h){d+=p(At.activationWidth/2-1)}if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)){d+=p(3)}}const u=[r,n,i,o];const g=Math.abs(l-d);if(t.wrap&&t.message){t.message=s.u.wrapLabel(t.message,s.e.getMax(g+2*At.wrapPadding,At.width),Dt(At))}const f=s.u.calculateTextDimensions(t.message,Dt(At));return{width:s.e.getMax(t.wrap?0:f.width+2*At.wrapPadding,g+2*At.wrapPadding,At.width),height:0,startx:l,stopx:d,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,u),toBounds:Math.max.apply(null,u)}};const Qt=function(t,e,a,r){const n={};const i=[];let o,c,l;t.forEach((function(t){t.id=s.u.random({length:10});switch(t.type){case r.db.LINETYPE.LOOP_START:case r.db.LINETYPE.ALT_START:case r.db.LINETYPE.OPT_START:case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:case r.db.LINETYPE.CRITICAL_START:case r.db.LINETYPE.BREAK_START:i.push({id:t.id,msg:t.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case r.db.LINETYPE.ALT_ELSE:case r.db.LINETYPE.PAR_AND:case r.db.LINETYPE.CRITICAL_OPTION:if(t.message){o=i.pop();n[o.id]=o;n[t.id]=o;i.push(o)}break;case r.db.LINETYPE.LOOP_END:case r.db.LINETYPE.ALT_END:case r.db.LINETYPE.OPT_END:case r.db.LINETYPE.PAR_END:case r.db.LINETYPE.CRITICAL_END:case r.db.LINETYPE.BREAK_END:o=i.pop();n[o.id]=o;break;case r.db.LINETYPE.ACTIVE_START:{const a=e[t.from?t.from.actor:t.to.actor];const s=Wt(t.from?t.from.actor:t.to.actor).length;const r=a.x+a.width/2+(s-1)*At.activationWidth/2;const n={startx:r,stopx:r+At.activationWidth,actor:t.from.actor,enabled:true};St.activations.push(n)}break;case r.db.LINETYPE.ACTIVE_END:{const e=St.activations.map((t=>t.actor)).lastIndexOf(t.from.actor);delete St.activations.splice(e,1)[0]}break}const a=t.placement!==void 0;if(a){c=Jt(t,e,r);t.noteModel=c;i.forEach((t=>{o=t;o.from=s.e.getMin(o.from,c.startx);o.to=s.e.getMax(o.to,c.startx+c.width);o.width=s.e.getMax(o.width,Math.abs(o.from-o.to))-At.labelBoxWidth}))}else{l=Zt(t,e,r);t.msgModel=l;if(l.startx&&l.stopx&&i.length>0){i.forEach((a=>{o=a;if(l.startx===l.stopx){const a=e[t.from];const r=e[t.to];o.from=s.e.getMin(a.x-l.width/2,a.x-a.width/2,o.from);o.to=s.e.getMax(r.x+l.width/2,r.x+a.width/2,o.to);o.width=s.e.getMax(o.width,Math.abs(o.to-o.from))-At.labelBoxWidth}else{o.from=s.e.getMin(l.startx,o.from);o.to=s.e.getMax(l.stopx,o.to);o.width=s.e.getMax(o.width,l.width)-At.labelBoxWidth}}))}}}));St.activations=[];s.l.debug("Loop type widths:",n);return n};const te={bounds:St,drawActors:Vt,drawActorsPopup:Ft,setConf:qt,draw:jt};const ee={parser:p,db:G,renderer:te,styles:Z,init:({wrap:t})=>{G.setWrap(t)}}},40055:(t,e,a)=>{a.d(e,{a:()=>i,b:()=>l,c:()=>c,d:()=>n,e:()=>h,f:()=>o,g:()=>d});var s=a(16750);var r=a(76235);const n=(t,e)=>{const a=t.append("rect");a.attr("x",e.x);a.attr("y",e.y);a.attr("fill",e.fill);a.attr("stroke",e.stroke);a.attr("width",e.width);a.attr("height",e.height);e.rx!==void 0&&a.attr("rx",e.rx);e.ry!==void 0&&a.attr("ry",e.ry);if(e.attrs!==void 0){for(const t in e.attrs){a.attr(t,e.attrs[t])}}e.class!==void 0&&a.attr("class",e.class);return a};const i=(t,e)=>{const a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};const s=n(t,a);s.lower()};const o=(t,e)=>{const a=e.text.replace(r.H," ");const s=t.append("text");s.attr("x",e.x);s.attr("y",e.y);s.attr("class","legend");s.style("text-anchor",e.anchor);e.class!==void 0&&s.attr("class",e.class);const n=s.append("tspan");n.attr("x",e.x+e.textMargin*2);n.text(a);return s};const c=(t,e,a,r)=>{const n=t.append("image");n.attr("x",e);n.attr("y",a);const i=(0,s.Jf)(r);n.attr("xlink:href",i)};const l=(t,e,a,r)=>{const n=t.append("use");n.attr("x",e);n.attr("y",a);const i=(0,s.Jf)(r);n.attr("xlink:href",`#${i}`)};const d=()=>{const t={x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0};return t};const h=()=>{const t={x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:true};return t}}}]);