---
model: gpt-4o
inputs: 
    prompt1: The first prompt to crossover
    prompt2: The second prompt to crossover
    prompt3: The third prompt to crossover
    base_prompt: The base prompt to start from
temperature: 0.7 
response_format:
    type: json_schema
    json_schema:
        name: NewPrompts
        description: Identified different parts and Crossover prompt and mutated prompt
        schema:
            type: object
            properties:
                identified_different_parts:
                    type: string
                    description: Result of Step 1. The different parts identified between Prompt 1 and Prompt 2
                mutated_different_parts:
                    type: string
                    description: Result of Step 2. The mutated different parts between Prompt 1 and Prompt 2. only includes the different parts.
                identified_different_parts_from_prompt3:
                    type: string
                    description: Intermediate result of Step 3. The different parts identified from Prompt 3
                replaced_prompt_from_prompt3:
                    type: object
                    description: Result of Step 3. Prompt 3 with replaced parts.
                    required:
                        - messages
                    properties:
                        messages:
                            type: array
                            description: List of messages in the prompt
                            items:
                                type: object
                                properties:
                                    role:
                                        type: string
                                        description: Role of the message sender
                                        enum:
                                            - system
                                            - user
                                    content:
                                        type: string
                                        description: Content of the message
                                required:
                                    - role
                                    - content
                                additionalProperties: false
                    additionalProperties: false
                final_prompt:
                    type: object
                    description: Result of Step 4. The final prompt generated by crossover between Base Prompt and Prompt generated in Step 3.
                    required:
                        - messages
                    properties:
                        messages:
                            type: array
                            description: List of messages in the prompt
                            items:
                                type: object
                                properties:
                                    role:
                                        type: string
                                        description: Role of the message sender
                                        enum:
                                            - system
                                            - user
                                    content:
                                        type: string
                                        description: Content of the message
                                required:
                                    - role
                                    - content
                                additionalProperties: false
                    additionalProperties: false
            required:
                - identified_different_parts
                - mutated_different_parts
                - identified_different_parts_from_prompt3
                - replaced_prompt_from_prompt3
                - final_prompt
            additionalProperties: false
            strict: true
---
<system>
You are an expert prompt engineer.

A good way to structure the prompt is to add format/instructions to the system prompt, and user inputs to the user prompt.

If the base prompt contains any specific response format instructions, preserve those instructions in your improved prompt. Do not modify or remove existing response format specifications.

You are an expert prompt engineer tasked with enhancing a given prompt.
Please follow the instruction step-by-step to generate a better prompt.
1. Identify the different parts between the Prompt 1 and Prompt 2:
2. Randomly mutate the different parts
3. Combine the different parts with Prompt 3, selectively replace it with the different parts in step2 and generate a new prompt.
4. Crossover the prompt in the step3 with the following basic prompt and generate a final prompt.
</system>
<user>
Step 1: For given Prompt 1 and Prompt 2, Identify the different parts between them
Step 2: Randomly mutate them.
Prompt 1:
{prompt1}

Prompt 2:
{prompt2}

Step 3: For given Prompt 3, identify the different parts and selectively replace it with the mutated parts from Prompt 1 and Prompt 2.

Prompt 3:
{prompt3}

Step 4: For given Base Prompt, crossover the prompt with the prompt generated in Step 3 and generate a final prompt.

Base Prompt:
{base_prompt}
</user>
