"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[7642],{81809:(e,t,n)=>{n.d(t,{H:()=>l});var r=n(62322);function l(e,t){var n=e.append("foreignObject").attr("width","100000");var l=n.append("xhtml:div");l.attr("xmlns","http://www.w3.org/1999/xhtml");var o=t.label;switch(typeof o){case"function":l.insert(o);break;case"object":l.insert((function(){return o}));break;default:l.html(o)}r.AV(l,t.labelStyle);l.style("display","inline-block");l.style("white-space","nowrap");var a=l.node().getBoundingClientRect();n.attr("width",a.width).attr("height",a.height);return n}},62322:(e,t,n)=>{n.d(t,{AV:()=>c,De:()=>o,c$:()=>p,gh:()=>a,nh:()=>d});var r=n(78696);var l=n(58807);function o(e,t){return!!e.children(t).length}function a(e){return i(e.v)+":"+i(e.w)+":"+i(e.name)}var s=/:/g;function i(e){return e?String(e).replace(s,"\\:"):""}function c(e,t){if(t){e.attr("style",t)}}function d(e,t,n){if(t){e.attr("class",t).attr("class",n+" "+e.attr("class"))}}function p(e,t){var n=t.graph();if(r.A(n)){var o=n.transition;if(l.A(o)){return o(e)}}return e}},7642:(e,t,n)=>{n.d(t,{diagram:()=>u});var r=n(34050);var l=n(76706);var o=n(76235);var a=n(1056);var s=n(84416);var i=n(29);var c=n(93498);var d=n(74353);var p=n.n(d);var b=n(16750);var f=n(42838);var w=n.n(f);const u={parser:r.p,db:r.f,renderer:l.f,styles:l.a,init:e=>{if(!e.flowchart){e.flowchart={}}e.flowchart.arrowMarkerAbsolute=e.arrowMarkerAbsolute;(0,o.p)({flowchart:{arrowMarkerAbsolute:e.arrowMarkerAbsolute}});l.f.setConf(e.flowchart);r.f.clear();r.f.setGen("gen-2")}}},76706:(e,t,n)=>{n.d(t,{a:()=>m,f:()=>k});var r=n(84416);var l=n(1056);var o=n(76235);var a=n(71017);var s=n(81809);var i=n(57991);var c=n(63221);const d=(e,t)=>i.A.lang.round(c.A.parse(e)[t]);const p=d;var b=n(3635);const f={};const w=function(e){const t=Object.keys(e);for(const n of t){f[n]=e[n]}};const u=function(e,t,n,r,l,a){const i=r.select(`[id="${n}"]`);const c=Object.keys(e);c.forEach((function(n){const r=e[n];let c="default";if(r.classes.length>0){c=r.classes.join(" ")}c=c+" flowchart-label";const d=(0,o.k)(r.styles);let p=r.text!==void 0?r.text:r.id;let b;o.l.info("vertex",r,r.labelType);if(r.labelType==="markdown"){o.l.info("vertex",r,r.labelType)}else{if((0,o.m)((0,o.c)().flowchart.htmlLabels)){const e={label:p.replace(/fa[blrs]?:fa-[\w-]+/g,(e=>`<i class='${e.replace(":"," ")}'></i>`))};b=(0,s.H)(i,e).node();b.parentNode.removeChild(b)}else{const e=l.createElementNS("http://www.w3.org/2000/svg","text");e.setAttribute("style",d.labelStyle.replace("color:","fill:"));const t=p.split(o.e.lineBreakRegex);for(const n of t){const t=l.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve");t.setAttribute("dy","1em");t.setAttribute("x","1");t.textContent=n;e.appendChild(t)}b=e}}let f=0;let w="";switch(r.type){case"round":f=5;w="rect";break;case"square":w="rect";break;case"diamond":w="question";break;case"hexagon":w="hexagon";break;case"odd":w="rect_left_inv_arrow";break;case"lean_right":w="lean_right";break;case"lean_left":w="lean_left";break;case"trapezoid":w="trapezoid";break;case"inv_trapezoid":w="inv_trapezoid";break;case"odd_right":w="rect_left_inv_arrow";break;case"circle":w="circle";break;case"ellipse":w="ellipse";break;case"stadium":w="stadium";break;case"subroutine":w="subroutine";break;case"cylinder":w="cylinder";break;case"group":w="rect";break;case"doublecircle":w="doublecircle";break;default:w="rect"}t.setNode(r.id,{labelStyle:d.labelStyle,shape:w,labelText:p,labelType:r.labelType,rx:f,ry:f,class:c,style:d.style,id:r.id,link:r.link,linkTarget:r.linkTarget,tooltip:a.db.getTooltip(r.id)||"",domId:a.db.lookUpDomId(r.id),haveCallback:r.haveCallback,width:r.type==="group"?500:void 0,dir:r.dir,type:r.type,props:r.props,padding:(0,o.c)().flowchart.padding});o.l.info("setNode",{labelStyle:d.labelStyle,labelType:r.labelType,shape:w,labelText:p,rx:f,ry:f,class:c,style:d.style,id:r.id,domId:a.db.lookUpDomId(r.id),width:r.type==="group"?500:void 0,type:r.type,dir:r.dir,props:r.props,padding:(0,o.c)().flowchart.padding})}))};const h=function(e,t,n){o.l.info("abc78 edges = ",e);let r=0;let a={};let s;let i;if(e.defaultStyle!==void 0){const t=(0,o.k)(e.defaultStyle);s=t.style;i=t.labelStyle}e.forEach((function(n){r++;const c="L-"+n.start+"-"+n.end;if(a[c]===void 0){a[c]=0;o.l.info("abc78 new entry",c,a[c])}else{a[c]++;o.l.info("abc78 new entry",c,a[c])}let d=c+"-"+a[c];o.l.info("abc78 new link id to be used is",c,d,a[c]);const p="LS-"+n.start;const b="LE-"+n.end;const w={style:"",labelStyle:""};w.minlen=n.length||1;if(n.type==="arrow_open"){w.arrowhead="none"}else{w.arrowhead="normal"}w.arrowTypeStart="arrow_open";w.arrowTypeEnd="arrow_open";switch(n.type){case"double_arrow_cross":w.arrowTypeStart="arrow_cross";case"arrow_cross":w.arrowTypeEnd="arrow_cross";break;case"double_arrow_point":w.arrowTypeStart="arrow_point";case"arrow_point":w.arrowTypeEnd="arrow_point";break;case"double_arrow_circle":w.arrowTypeStart="arrow_circle";case"arrow_circle":w.arrowTypeEnd="arrow_circle";break}let u="";let h="";switch(n.stroke){case"normal":u="fill:none;";if(s!==void 0){u=s}if(i!==void 0){h=i}w.thickness="normal";w.pattern="solid";break;case"dotted":w.thickness="normal";w.pattern="dotted";w.style="fill:none;stroke-width:2px;stroke-dasharray:3;";break;case"thick":w.thickness="thick";w.pattern="solid";w.style="stroke-width: 3.5px;fill:none;";break;case"invisible":w.thickness="invisible";w.pattern="solid";w.style="stroke-width: 0;fill:none;";break}if(n.style!==void 0){const e=(0,o.k)(n.style);u=e.style;h=e.labelStyle}w.style=w.style+=u;w.labelStyle=w.labelStyle+=h;if(n.interpolate!==void 0){w.curve=(0,o.n)(n.interpolate,l.lUB)}else if(e.defaultInterpolate!==void 0){w.curve=(0,o.n)(e.defaultInterpolate,l.lUB)}else{w.curve=(0,o.n)(f.curve,l.lUB)}if(n.text===void 0){if(n.style!==void 0){w.arrowheadStyle="fill: #333"}}else{w.arrowheadStyle="fill: #333";w.labelpos="c"}w.labelType=n.labelType;w.label=n.text.replace(o.e.lineBreakRegex,"\n");if(n.style===void 0){w.style=w.style||"stroke: #333; stroke-width: 1.5px;fill:none;"}w.labelStyle=w.labelStyle.replace("color:","fill:");w.id=d;w.classes="flowchart-link "+p+" "+b;t.setEdge(n.start,n.end,w,r)}))};const g=function(e,t){return t.db.getClasses()};const y=async function(e,t,n,s){o.l.info("Drawing flowchart");let i=s.db.getDirection();if(i===void 0){i="TD"}const{securityLevel:c,flowchart:d}=(0,o.c)();const p=d.nodeSpacing||50;const b=d.rankSpacing||50;let f;if(c==="sandbox"){f=(0,l.Ltv)("#i"+t)}const w=c==="sandbox"?(0,l.Ltv)(f.nodes()[0].contentDocument.body):(0,l.Ltv)("body");const g=c==="sandbox"?f.nodes()[0].contentDocument:document;const y=new r.T({multigraph:true,compound:true}).setGraph({rankdir:i,nodesep:p,ranksep:b,marginx:0,marginy:0}).setDefaultEdgeLabel((function(){return{}}));let k;const v=s.db.getSubGraphs();o.l.info("Subgraphs - ",v);for(let r=v.length-1;r>=0;r--){k=v[r];o.l.info("Subgraph - ",k);s.db.addVertex(k.id,{text:k.title,type:k.labelType},"group",void 0,k.classes,k.dir)}const x=s.db.getVertices();const m=s.db.getEdges();o.l.info("Edges",m);let S=0;for(S=v.length-1;S>=0;S--){k=v[S];(0,l.Ubm)("cluster").append("text");for(let e=0;e<k.nodes.length;e++){o.l.info("Setting up subgraphs",k.nodes[e],k.id);y.setParent(k.nodes[e],k.id)}}u(x,y,t,w,g,s);h(m,y);const T=w.select(`[id="${t}"]`);const _=w.select("#"+t+" g");await(0,a.r)(_,y,["point","circle","cross"],"flowchart",t);o.u.insertTitle(T,"flowchartTitleText",d.titleTopMargin,s.db.getDiagramTitle());(0,o.o)(y,T,d.diagramPadding,d.useMaxWidth);s.db.indexNodes("subGraph"+S);if(!d.htmlLabels){const e=g.querySelectorAll('[id="'+t+'"] .edgeLabel .label');for(const t of e){const e=t.getBBox();const n=g.createElementNS("http://www.w3.org/2000/svg","rect");n.setAttribute("rx",0);n.setAttribute("ry",0);n.setAttribute("width",e.width);n.setAttribute("height",e.height);t.insertBefore(n,t.firstChild)}}const C=Object.keys(x);C.forEach((function(e){const n=x[e];if(n.link){const r=(0,l.Ltv)("#"+t+' [id="'+e+'"]');if(r){const e=g.createElementNS("http://www.w3.org/2000/svg","a");e.setAttributeNS("http://www.w3.org/2000/svg","class",n.classes.join(" "));e.setAttributeNS("http://www.w3.org/2000/svg","href",n.link);e.setAttributeNS("http://www.w3.org/2000/svg","rel","noopener");if(c==="sandbox"){e.setAttributeNS("http://www.w3.org/2000/svg","target","_top")}else if(n.linkTarget){e.setAttributeNS("http://www.w3.org/2000/svg","target",n.linkTarget)}const t=r.insert((function(){return e}),":first-child");const l=r.select(".label-container");if(l){t.append((function(){return l.node()}))}const o=r.select(".label");if(o){t.append((function(){return o.node()}))}}}}))};const k={setConf:w,addVertices:u,addEdges:h,getClasses:g,draw:y};const v=(e,t)=>{const n=p;const r=n(e,"r");const l=n(e,"g");const o=n(e,"b");return b.A(r,l,o,t)};const x=e=>`.label {\n    font-family: ${e.fontFamily};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .cluster-label text {\n    fill: ${e.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${e.titleColor};\n  }\n\n  .label text,span,p {\n    fill: ${e.nodeTextColor||e.textColor};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${e.mainBkg};\n    stroke: ${e.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${e.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${e.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${e.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${e.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${e.edgeLabelBackground};\n      fill: ${e.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${v(e.edgeLabelBackground,.5)};\n    // background-color: \n  }\n\n  .cluster rect {\n    fill: ${e.clusterBkg};\n    stroke: ${e.clusterBorder};\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${e.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${e.titleColor};\n  }\n  /* .cluster div {\n    color: ${e.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${e.fontFamily};\n    font-size: 12px;\n    background: ${e.tertiaryColor};\n    border: 1px solid ${e.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${e.textColor};\n  }\n`;const m=x}}]);