<!--
  ~ Copyright (c) Jupyter Development Team.
  ~ Distributed under the terms of the Modified BSD License.
-->

<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= htmlWebpackPlugin.options.title %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <%= require('html-loader!./partial.html') %>
</head>
<body class="jp-ThemedContainer">

<script type="text/javascript">
  /* Remove token from URL. */
  (function () {
    var location = window.location;
    var search = location.search;

    // If there is no query string, bail.
    if (search.length <= 1) {
      return;
    }

    // Rebuild the query string without the `token`.
    var query = '?' + search.slice(1).split('&')
      .filter(function (param) { return param.split('=')[0] !== 'token'; })
      .join('&');

    // Rebuild the URL with the new query string.
    var url = location.origin + location.pathname +
      (query !== '?' ? query : '') + location.hash;

    if (url === location.href) {
      return;
    }

    window.history.replaceState({ }, '', url);
  })();
</script>

</body>
</html>
