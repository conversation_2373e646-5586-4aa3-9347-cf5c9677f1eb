{"name": "@jupyterlab/statusbar-extension", "version": "4.3.6", "description": "JupyterLab - Statusbar Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.d.ts", "lib/**/*.js.map", "lib/**/*.js", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/statusbar": "^4.3.6", "@jupyterlab/translation": "^4.3.6"}, "devDependencies": {"@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}