
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-03-04T16:04:14-0800",
 "dirty": false,
 "error": null,
 "full-revisionid": "96de4376a5e61046d4348f3ff7ae73f076805649",
 "version": "1.8.13"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
