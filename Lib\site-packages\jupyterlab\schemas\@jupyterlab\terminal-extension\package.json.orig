{"name": "@jupyterlab/terminal-extension", "version": "4.3.6", "description": "JupyterLab - Terminal Emulator Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/launcher": "^4.3.6", "@jupyterlab/mainmenu": "^4.3.6", "@jupyterlab/running": "^4.3.6", "@jupyterlab/services": "^7.3.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/terminal": "^4.3.6", "@jupyterlab/translation": "^4.3.6", "@jupyterlab/ui-components": "^4.3.6", "@lumino/widgets": "^2.5.0"}, "devDependencies": {"@types/webpack-env": "^1.18.0", "rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}