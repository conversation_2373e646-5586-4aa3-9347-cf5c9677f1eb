---
model: gpt-4o
description: Reformat the given prompt template so that a JSON output is enforced, and a placeholder variable for few shot examples is added.
inputs:
    prompt: The prompt that needs to be reformatted.
response_format:
    type: json_schema
    json_schema:
        name: prompt
        description: Creates a prompt consisting of a list of messages.
        strict: true
        schema:
            type: object
            required:
                - messages
            properties:
                messages:
                    type: array
                    description: List of messages in the prompt
                    items:
                        type: object
                        properties:
                            role:
                                type: string
                                description: Role of the message sender
                                enum:
                                    - system
                                    - user
                            content:
                                type: string
                                description: Content of the message
                        required:
                            - role
                            - content
                        additionalProperties: false
            additionalProperties: false
---
<system>
You are an expert prompt engineer.
Your task is to reformat the given prompt template so that a the prompt enforces a JSON output, and a placeholder variable for few shot examples {_FEWSHOT_} is added.
** IMPORTANT: The formatted prompt must include the word JSON in the prompt. **

Below is an example.

Input prompt:
{
    "messages": [
        {
            "role": "system",
            "content": """Answer questions with short factoid answers.

Output only the answer."""
        },
        {
            "role": "user",
            "content": """Some instructions.

Question:
{question}"""
        }
    ]
}

Formatted prompt:
{
    "messages": [
        {
            "role": "system",
            "content": """Answer questions with short factoid answers.

Your output must be formatted as a JSON object."""
        },
        {
            "role": "user",
            "content": """Some user prompt.

Task demonstrations:
{_FEWSHOT_}

Now perform the task for the following question:
{question}"""
        }
    ]
}
</system>
<user>
Now given the input, generate the formatted prompt.
Input prompt:
{prompt}

Formatted prompt:
</user>