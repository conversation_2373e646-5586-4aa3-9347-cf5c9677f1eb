from vnpy_ctabacktester import CtaBacktesterApp
from typing import Dict, List
import os
import sys
import io
import logging
import traceback
import re
import tempfile
import webbrowser
# vnpy导入
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)

# 使用标准库而非本地实现
from datetime import datetime, timedelta

# 第三方库导入
import numpy as np
import pandas as pd

class AlphaSignalStrategy(CtaTemplate):
    """
    基于Alpha101因子的CTA策略
    
    该策略结合多个Alpha因子生成交易信号，并根据市场波动性动态调整仓位。
    """

    author = "AlphaTrader"
    
    # 策略参数
    alpha_factors = ["alpha001", "alpha008", "alpha012", "alpha017"]  # 选择的Alpha因子
    factor_weights = [0.25, 0.25, 0.25, 0.25]                       # 各因子权重，和为1
    threshold = 0.5                                                  # 信号阈值
    lookback_days = 20                                               # 获取因子的回看天数
    risk_percent = 0.02                                              # 风险比例
    atr_multiplier = 2                                               # ATR倍数
    test_factor_only = True                                         # 仅测试因子，不执行交易
    
    # 策略变量
    combined_alpha = 0.0                                             # 综合Alpha值
    signal = 0                                                       # 交易信号 (1:买入, 0:不操作/卖出)
    last_alpha_time = None                                           # 上次获取因子的时间

    # 参数和变量列表
    parameters = ["alpha_factors", "factor_weights", "threshold", "lookback_days", "risk_percent", "atr_multiplier", "test_factor_only"]
    variables = ["combined_alpha", "signal"]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        # 先调用父类的初始化，确保cta_engine被正确设置
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 检查系统环境
        self._check_system_environment()
        
        # 添加文件日志记录
        self._setup_log_file(strategy_name)
        self._log_to_file(f"\n==== 策略初始化: {strategy_name}, 交易品种: {vt_symbol} ====")
        
        # 处理参数
        self._process_alpha_factors(setting)
        self._process_factor_weights(setting)
        
        # 创建K线生成器和数组管理器
        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager()
        
        # 加载factor_alpha101模块
        try:
            self._load_alpha_module()
            # 成功导入消息将在_load_alpha_module中输出
            sys.stdout.write("Alpha因子计算模块加载完成\n")
            sys.stdout.flush()
            # 显示模块加载详情
            self._display_module_loading_summary()
        except Exception as e:
            sys.stdout.write(f"Alpha模块加载失败: {str(e)}\n")
            sys.stdout.flush()
        
        # 初始化其他变量
        self.factor_values = {}
        self.last_alpha_time = None
        
        # 新增: 用于记录因子历史值的字典
        self.factor_history = {factor: [] for factor in self.alpha_factors}
        self.combined_alpha_history = []
        self.signal_history = []
        self.datetime_history = []

        sys.stdout.write("\n==== 策略初始化完成，参数设置: ====\n")
        sys.stdout.write(f"- 因子: {self.alpha_factors}\n")
        sys.stdout.write(f"- 权重: {self.factor_weights}\n")
        sys.stdout.write(f"- 阈值: {self.threshold}\n")
        sys.stdout.write("====================================\n\n")
        sys.stdout.flush()
        
        # 记录详细的参数处理信息
        self._log_to_file("策略参数处理结果:")
        self._log_to_file(f"- 因子列表: {self.alpha_factors}")
        self._log_to_file(f"- 权重列表: {self.factor_weights}")
        self._log_to_file(f"- 权重数量: {len(self.factor_weights)}, 因子数量: {len(self.alpha_factors)}")
        self._log_to_file(f"- 阈值: {self.threshold}")

    def _load_alpha_module(self):
        """加载factor_alpha101模块"""
        try:
            # 添加策略路径
            vnpy_strategy_path = r"C:\veighna_studio\Lib\site-packages\vnpy_ctastrategy\strategies"
            if os.path.exists(vnpy_strategy_path) and vnpy_strategy_path not in sys.path:
                sys.path.append(vnpy_strategy_path)
                self.write_log(f"已添加策略路径: {vnpy_strategy_path}")
            
            # 先检查模块是否已存在
            try:
                # 移除本地导入，使用全局导入的sys
                if 'factor_alpha101' in sys.modules:
                    self.alpha_module = sys.modules['factor_alpha101'] 
                    self.write_log(f"【模块加载】从已加载模块中获取factor_alpha101")
                    sys.stdout.write(f"成功导入factor_alpha101(从sys.modules获取)\n")
                    sys.stdout.flush()
                    return
            except Exception as e:
                self.write_log(f"检查模块是否存在时出错: {str(e)}")
                pass
            
            # 尝试动态导入factor_alpha101
            try:
                # 列出可能的模块路径
                possible_paths = [
                    os.path.join(vnpy_strategy_path, "factor_alpha101.py"),
                    os.path.join(vnpy_strategy_path, "alpha101.py"),
                    os.path.join(vnpy_strategy_path, "alphas101.py"),
                    os.path.join(os.path.dirname(vnpy_strategy_path), "factor_alpha101.py")
                ]
                
                self.write_log("【模块查找】检查可能的Alpha模块文件路径:")
                sys.stdout.write("开始查找Alpha因子模块...\n")
                sys.stdout.flush()
                
                found_files = []
                for i, path in enumerate(possible_paths):
                    exists = os.path.exists(path)
                    status = "可用" if exists else "不可用"
                    self.write_log(f"  {i+1}. {path}: {status}")
                    if exists:
                        found_files.append(path)
                        sys.stdout.write(f"找到模块文件: {path}\n")
                        sys.stdout.flush()
                
                if not found_files:
                    sys.stdout.write("未找到任何Alpha因子模块文件\n")
                    sys.stdout.flush()
                
                # 尝试查找模块文件并加载
                for path in possible_paths:
                    if os.path.exists(path):
                        # 使用importlib.machinery替代importlib.util
                        from importlib.machinery import SourceFileLoader
                        module_name = os.path.splitext(os.path.basename(path))[0]
                        factor_alpha101 = SourceFileLoader(module_name, path).load_module()
                        
                        # 赋值给策略实例
                        self.alpha_module = factor_alpha101
                        module_name = os.path.splitext(os.path.basename(path))[0]
                        self.write_log(f"【模块加载】成功从路径加载alpha模块: {path}")
                        sys.stdout.write(f"成功导入{module_name}模块，从文件: {path}\n")
                        sys.stdout.flush()
                        
                        # 输出模块信息
                        self._print_module_info(factor_alpha101)
                        return
                    
                # 如果没有找到任何模块文件
                self.write_log("【错误】未找到任何Alpha因子模块文件")
                error_msg = "未找到factor_alpha101.py文件或替代文件"
                sys.stdout.write(f"错误: {error_msg}\n")
                sys.stdout.write("请检查以下可能的路径:\n")
                for path in possible_paths:
                    sys.stdout.write(f" - {path}\n")
                sys.stdout.flush()
                raise FileNotFoundError(error_msg)
                
            except Exception as inner_e:
                self.write_log(f"动态加载factor_alpha101模块失败: {str(inner_e)}")
                raise ImportError(f"无法导入factor_alpha101模块: {str(inner_e)}")
                
        except ImportError as e:
            self.write_log(f"加载factor_alpha101模块失败: {str(e)}")
            raise ImportError("无法导入factor_alpha101模块，请确保该文件存在于策略目录")

    def _print_module_info(self, module):
        """打印模块信息"""
        try:
            self.write_log("\n==== Alpha因子模块信息 ====")
            
            # 获取模块文件路径
            if hasattr(module, '__file__'):
                self.write_log(f"模块文件路径: {module.__file__}")
            
            # 检查模块是否有Alphas类
            has_alphas_class = hasattr(module, 'Alphas')
            self.write_log(f"Alphas类: {'可用' if has_alphas_class else '不可用'}")
            
            if has_alphas_class:
                # 尝试获取所有因子方法
                alphas_class = getattr(module, 'Alphas')
                alpha_methods = [m for m in dir(alphas_class) if callable(getattr(alphas_class, m)) and not m.startswith('_')]
                self.write_log(f"找到{len(alpha_methods)}个可能的因子方法")
                
                # 检查策略中使用的因子是否存在
                available_factors = []
                missing_factors = []
                for factor in self.alpha_factors:
                    exists = factor in alpha_methods
                    status = "可用" if exists else "不可用"
                    self.write_log(f"因子 {factor}: {status}")
                    if exists:
                        available_factors.append(factor)
                    else:
                        missing_factors.append(factor)
                
                # 输出汇总信息
                if len(available_factors) == len(self.alpha_factors):
                    self.write_log("所有需要的因子均可用 [OK]")
                else:
                    self.write_log(f"警告: 只有 {len(available_factors)}/{len(self.alpha_factors)} 个因子可用")
                    if missing_factors:
                        self.write_log(f"缺少因子: {', '.join(missing_factors)}")
            else:
                self.write_log("警告: 模块不包含Alphas类，无法使用Alpha因子")
        
            self.write_log("==== 模块信息结束 ====\n")
        except Exception as e:
            self.write_log(f"打印模块信息时出错: {str(e)}")

    def _process_alpha_factors(self, setting):
        """处理alpha_factors参数"""
        if "alpha_factors" in setting:
            if isinstance(setting["alpha_factors"], list):
                # 检查列表中的元素是否是单个字符 (如因子名被拆分成字符列表)
                if len(setting["alpha_factors"]) > 0 and all(isinstance(item, str) and len(item) <= 1 for item in setting["alpha_factors"]):
                    self.write_log(f"检测到单字符列表，尝试重建因子名称")
                    try:
                        # 将所有字符连接起来
                        combined_str = ''.join(setting["alpha_factors"])
                        # 尝试解析成列表
                        if '[' in combined_str and ']' in combined_str:
                            # 看起来像是字符串化的列表
                            import re
                            # 提取引号内的内容作为因子名
                            factors = re.findall(r"'([^']*)'|\"([^\"]*)\"", combined_str)
                            # findall返回的是元组，需要处理一下
                            self.alpha_factors = [f[0] if f[0] else f[1] for f in factors]
                            self.write_log(f"成功从字符列表构建因子: {self.alpha_factors}")
                        else:
                            raise ValueError("无法从字符列表重建因子名称列表")
                    except Exception as e:
                        self.write_log(f"重建因子名称失败: {str(e)}，使用默认因子")
                        self.alpha_factors = ["alpha001", "alpha008", "alpha012", "alpha017"]
                else:
                    # 正常列表处理
                    self.alpha_factors = setting["alpha_factors"]
            elif isinstance(setting["alpha_factors"], str):
                # 移除可能的方括号和引号
                cleaned = setting["alpha_factors"].strip("[]() ")
                self.alpha_factors = [x.strip().strip("'\"") for x in cleaned.split(",")]

    def _process_factor_weights(self, setting):
        """处理factor_weights参数"""
        if "factor_weights" in setting:
            # 直接接收单个数值情况
            if isinstance(setting["factor_weights"], (int, float)):
                # 如果是单个数值，将其复制为所有因子的权重
                weight_value = float(setting["factor_weights"]) 
                self.factor_weights = [weight_value] * len(self.alpha_factors)
                self.write_log(f"使用单一权重值: {weight_value} 生成因子权重列表")
            elif isinstance(setting["factor_weights"], list):
                # 列表中的元素可能还是字符串，需要确保转换为浮点数
                try:
                    self.factor_weights = [float(w) for w in setting["factor_weights"]]
                except (ValueError, TypeError):
                    # 如果列表中元素无法转换为浮点数，记录错误并使用等权重
                    self.write_log(f"无法解析权重列表: {setting['factor_weights']}，使用默认等权重")
                    self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
            elif isinstance(setting["factor_weights"], str):
                # 可能是字符串化的列表，例如 "[0.25, 0.25, 0.25, 0.25]"
                self._parse_weights_from_string(setting["factor_weights"])
            else:
                # 其他不支持的类型
                self.write_log(f"不支持的权重参数类型: {type(setting['factor_weights'])}，使用默认等权重")
                self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
    
        # 确保权重与因子数量相等
        self._validate_weights()

    def _parse_weights_from_string(self, weights_str):
        """从字符串解析权重"""
        try:
            # 移除可能存在的方括号、括号和多余空格
            cleaned = weights_str.strip()
            # 检查是否是字符串化的列表 "[0.25, 0.25, 0.25, 0.25]"
            if cleaned.startswith("[") and cleaned.endswith("]"):
                cleaned = cleaned[1:-1]
            elif cleaned.startswith("(") and cleaned.endswith(")"):
                cleaned = cleaned[1:-1]
        
            # 分割并清理各权重值
            parts = cleaned.split(",")
            self.factor_weights = []
            for part in parts:
                part = part.strip().strip("'\"")
                if part:  # 确保不是空字符串
                    self.factor_weights.append(float(part))
                
            if not self.factor_weights:
                raise ValueError("解析后权重列表为空")
            
        except Exception as e:
            self.write_log(f"解析权重字符串失败: {weights_str}, 错误: {e}, 使用默认等权重")
            # 使用等权重
            self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)

    def _validate_weights(self):
        """验证和标准化权重"""
        # 调整权重数量
        if len(self.factor_weights) != len(self.alpha_factors):
            self.write_log(f"警告: 权重数量({len(self.factor_weights)})与因子数量({len(self.alpha_factors)})不匹配")
            # 如果权重不足，添加0权重
            if len(self.factor_weights) < len(self.alpha_factors):
                self.factor_weights.extend([0] * (len(self.alpha_factors) - len(self.factor_weights)))
            # 如果权重过多，截断
            else:
                self.factor_weights = self.factor_weights[:len(self.alpha_factors)]
    
        # 验证权重参数
        for i, weight in enumerate(self.factor_weights):
            if not isinstance(weight, (int, float)):
                self.write_log(f"警告: 权重应为数值类型，将第{i+1}个权重从{weight}调整为0")
                self.factor_weights[i] = 0
            elif weight < 0:
                self.write_log(f"警告: 因子权重不应为负数，已将第{i+1}个权重从{weight}调整为0")
                self.factor_weights[i] = 0
    
        # 标准化权重，确保和为1
        weight_sum = sum(self.factor_weights)
        if weight_sum > 0:
            self.factor_weights = [w/weight_sum for w in self.factor_weights]
        else:
            # 如果所有权重都是0，使用等权重
            self.factor_weights = [1.0/len(self.factor_weights) for _ in self.factor_weights]
    
        self.write_log(f"最终使用的因子: {self.alpha_factors}")
        self.write_log(f"最终使用的权重: {self.factor_weights}")

    def on_init(self):
        """策略初始化完成回调"""
        import sys
        sys.stdout.write("\n===== 策略on_init被调用 =====\n")
        sys.stdout.flush()
        
        # 打印原始参数信息，便于调试
        self._print_raw_parameters()
        
        self.write_log("策略初始化")
        
        # 输出初始化参数
        sys.stdout.write("策略参数确认:\n")
        sys.stdout.write(f"- 因子: {self.alpha_factors}\n")
        sys.stdout.write(f"- 权重: {self.factor_weights}\n") 
        sys.stdout.write(f"- 阈值: {self.threshold}\n")
        sys.stdout.write(f"- 回看天数: {self.lookback_days}\n")
        sys.stdout.flush()
        
        # 计算并加载足够的历史数据
        if hasattr(self, 'interval') and self.interval != '1d':
            # 分钟级数据需要更多的K线
            bars_needed = self._calculate_lookback_bars()
            self.load_bar(bars_needed)
            sys.stdout.write(f"已请求加载{bars_needed}条{self.interval}K线数据 (等效于{self.lookback_days}天)\n")
        else:
            # 日线数据直接使用天数
            self.load_bar(self.lookback_days)
            sys.stdout.write(f"已请求加载{self.lookback_days}天的历史数据\n")
        sys.stdout.flush()

    def _print_raw_parameters(self):
        """打印原始参数信息，便于调试"""
        try:
            # 获取所有已定义的参数名
            param_names = getattr(self, "parameters", [])
            
            # 打印原始参数
            self.write_log("\n===== 原始参数信息 =====")
            for name in param_names:
                value = getattr(self, name, "未设置")
                value_type = type(value).__name__
                self.write_log(f"参数 {name}: 类型={value_type}, 值={value}")
                
                # 对列表类型参数进行特殊处理
                if isinstance(value, list) and value:
                    first_elem_type = type(value[0]).__name__
                    self.write_log(f"  - 列表元素类型: {first_elem_type}")
                    self.write_log(f"  - 列表长度: {len(value)}")
                    if len(value) > 20:  # 列表太长时只显示部分
                        self.write_log(f"  - 前几个元素: {value[:5]}")
                        self.write_log(f"  - 后几个元素: {value[-5:]}")
                    else:
                        self.write_log(f"  - 元素内容: {value}")
            self.write_log("=========================")
        except Exception as e:
            self.write_log(f"打印原始参数时出错: {str(e)}")
            

    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
        self.put_event()

    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        self._log_to_file("策略停止执行，开始生成分析报告")
        
        # 在控制台输出开始提示信息
        sys.stdout.write("\n===== 策略已停止，正在生成因子分析报告 =====\n")
        sys.stdout.flush()
        
        # 输出日志文件位置
        if hasattr(self, 'log_filepath') and self.log_filepath:
            log_msg = f"策略日志文件: {self.log_filepath}"
            sys.stdout.write(f"{log_msg}\n")
            self._log_to_file(log_msg)
            
            # 检查日志文件是否存在和大小
            try:
                if os.path.exists(self.log_filepath):
                    filesize = os.path.getsize(self.log_filepath)
                    sys.stdout.write(f"日志文件大小: {filesize} 字节\n\n")
                    if filesize == 0:
                        sys.stdout.write("警告: 日志文件似乎为空！\n\n")
                else:
                    sys.stdout.write("警告: 日志文件未找到！\n\n")
            except Exception as log_check_err:
                sys.stdout.write(f"检查日志文件时出错: {log_check_err}\n\n")
        
        # 保存输出状态，用于后面的UI更新
        has_successful_output = False
        
        # 输出因子表现总结
        sys.stdout.write("步骤1: 生成因子表现总结...\n")
        sys.stdout.flush()
        self._log_to_file("正在生成因子表现总结")
        try:
            self._generate_factor_summary()
            sys.stdout.write("因子表现总结生成完成\n\n")
        except Exception as summary_err:
            sys.stdout.write(f"生成因子表现总结时出错: {summary_err}\n\n")
            self._log_to_file(f"生成因子表现总结失败: {summary_err}")
        
        # 评估因子预测正确率
        sys.stdout.write("步骤2: 评估因子预测正确率...\n")
        sys.stdout.flush()
        self._log_to_file("正在评估因子预测正确率")
        try:
            if len(self.datetime_history) > 10:  # 确保有足够的数据点
                self.evaluate_factor_accuracy()
                sys.stdout.write("因子预测正确率评估完成\n\n")
            else:
                sys.stdout.write(f"历史数据点数({len(self.datetime_history)})不足10个，跳过因子预测评估\n\n")
                self._log_to_file(f"历史数据点数({len(self.datetime_history)})不足10个，跳过因子预测评估")
        except Exception as accuracy_err:
            sys.stdout.write(f"评估因子预测正确率时出错: {accuracy_err}\n\n")
            self._log_to_file(f"评估因子预测正确率失败: {accuracy_err}")
        
        # 生成因子趋势报告
        sys.stdout.write("步骤3: 生成因子趋势文本报告...\n")
        sys.stdout.flush()
        self._log_to_file("正在生成因子趋势报告")
        try:
            self.generate_factor_report()
            sys.stdout.write("因子趋势报告生成完成\n\n")
        except Exception as report_err:
            sys.stdout.write(f"生成因子趋势报告时出错: {report_err}\n\n")
            self._log_to_file(f"生成因子趋势报告失败: {report_err}")
        
        # 保存因子数据到CSV文件
        sys.stdout.write("步骤4: 保存因子数据到CSV文件...\n")
        sys.stdout.flush()
        self._log_to_file("正在保存因子数据到CSV文件")
        try:
            csv_path = self.save_factor_data_to_csv()
            if csv_path and os.path.exists(csv_path):
                filesize = os.path.getsize(csv_path)
                sys.stdout.write(f"CSV数据文件已保存: {csv_path} (大小: {filesize} 字节)\n\n")
                self._log_to_file(f"CSV数据文件已保存成功: {csv_path}")
                has_successful_output = True
            else:
                sys.stdout.write(f"CSV数据文件保存失败或文件未创建\n\n")
                self._log_to_file("CSV数据文件保存失败")
        except Exception as csv_err:
            sys.stdout.write(f"保存CSV文件时出错: {csv_err}\n\n")
            self._log_to_file(f"保存CSV文件失败: {csv_err}")
        
        # 生成HTML报告
        sys.stdout.write("步骤5: 生成HTML交互式报告...\n")
        sys.stdout.flush()
        self._log_to_file("正在生成HTML报告")
        try:
            html_path = self.generate_html_report()
            if html_path and os.path.exists(html_path):
                filesize = os.path.getsize(html_path)
                sys.stdout.write(f"HTML报告已保存: {html_path} (大小: {filesize} 字节)\n\n")
                self._log_to_file(f"HTML报告已保存成功: {html_path}")
                has_successful_output = True
                
                # 额外提醒用户打开HTML报告
                sys.stdout.write("\n【重要】请使用浏览器打开以下文件查看交互式分析报告:\n")
                sys.stdout.write(f"{html_path}\n\n")
            else:
                sys.stdout.write(f"HTML报告生成失败或文件未创建\n\n")
                self._log_to_file("HTML报告生成失败")
        except Exception as html_err:
            sys.stdout.write(f"生成HTML报告时出错: {html_err}\n\n")
            self._log_to_file(f"生成HTML报告失败: {html_err}")
        
        # 最终总结
        if has_successful_output:
            sys.stdout.write("\n=== 分析报告生成完成 ===\n")
        else:
            sys.stdout.write("\n=== 警告: 所有分析报告生成失败 ===\n")
            sys.stdout.write("请检查程序是否有写入权限，或尝试以管理员身份运行程序。\n")
            
        # 显示工作目录信息，帮助用户定位文件
        sys.stdout.write(f"\n当前工作目录: {os.getcwd()}\n")
        sys.stdout.write("用户主目录: " + os.path.expanduser("~") + "\n")
        sys.stdout.write("临时目录: " + tempfile.gettempdir() + "\n\n")
        
        # 最后的建议
        sys.stdout.write("如果报告文件未能在预期位置找到，请检查上述路径\n")
        sys.stdout.write("也可以搜索计算机中最近创建的HTML或CSV文件\n")
        sys.stdout.flush()
        
        # 记录到日志
        self._log_to_file("策略停止流程完成")
        
        # 更新UI
        self.put_event()
        sys.stdout.flush()
        
        self.put_event()

    def on_tick(self, tick: TickData):
        """Tick数据更新回调"""
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData):
        """K线数据更新回调"""
        # 更新数据到数组管理器
        self.am.update_bar(bar)
        if not self.am.inited:
            self.write_log("ArrayManager尚未初始化完成")
            return

        # 如果不是测试模式，取消所有订单
        if not self.test_factor_only:
            self.cancel_all()
    
        # 检查数据是否足够
        if len(self.am.close_array) < 10:
            self.write_log("历史数据不足10条，暂不生成交易信号")
            return
        else:
            self.write_log(f"当前已有{len(self.am.close_array)}条历史数据")

        # 检查是否需要更新Alpha因子
        current_time = bar.datetime
        self.write_log(f"处理K线时间: {current_time}")
        
        # 在测试模式下，每次都强制更新因子
        should_update = True if self.test_factor_only else self._should_update_alpha(current_time)
        
        if should_update:
            self.write_log("需要更新Alpha因子")
            if not self._fetch_alpha_factors():
                self.write_log("获取Alpha因子失败，跳过本次交易信号生成")
                return
            
            # 记录因子历史值
            for factor, value in self.factor_values.items():
                if factor in self.factor_history:
                    self.factor_history[factor].append(value)
            self.datetime_history.append(current_time)
            
            # 测试模式下，输出详细的单因子值
            if self.test_factor_only:
                self._print_factor_details()
        else:
            self.write_log("使用缓存的Alpha因子")

        # 更新综合Alpha值
        try:
            self.combined_alpha = self._calculate_combined_alpha()
            self.write_log(f"计算综合Alpha值: {self.combined_alpha}")
        except ValueError as e:
            self.write_log(f"错误: {e}")
            return

        # 根据综合Alpha值生成交易信号
        self.signal = 1 if self.combined_alpha > self.threshold else 0
        self.signal_history.append(self.signal)
        self.write_log(f"生成交易信号: {self.signal} (1-买入, 0-卖出)")

        # 如果不是测试模式，执行交易策略
        if not self.test_factor_only:
            self._execute_trading_strategy(bar)
        else:
            # 测试模式下，只输出信号信息，不执行交易
            if self.signal == 1:
                self.write_log(f"[测试模式] 生成买入信号: Alpha={self.combined_alpha:.4f} > 阈值={self.threshold}")
            else:
                self.write_log(f"[测试模式] 生成卖出信号: Alpha={self.combined_alpha:.4f} <= 阈值={self.threshold}")
    
        # 更新UI
        self.put_event()
        
        # 每5个bar打印一次因子趋势（测试模式下每次都打印）
        if self.test_factor_only or (len(self.datetime_history) % 5 == 0):
            self._print_factor_trends()

    def _should_update_alpha(self, current_time):
        """判断是否需要更新Alpha因子"""
        # 如果没有上次计算的时间或因子值为空，则需要更新
        if not self.last_alpha_time or not self.factor_values:
            return True
        
        # 根据K线周期决定更新频率
        interval = getattr(self, 'interval', '1d')  # 默认日线
        
        if interval == '1d':  # 日线数据
            # 如果是同一天且已经有因子值，不重复计算
            if current_time.date() == self.last_alpha_time.date():
                return False
        elif '1m' in interval:  # 分钟级数据
            # 对于1分钟线，每15分钟更新一次因子
            minutes_diff = (current_time - self.last_alpha_time).total_seconds() / 60
            if minutes_diff < 15:  # 15分钟内不重复计算
                return False
        elif '5m' in interval:  # 5分钟线
            # 对于5分钟线，每30分钟更新一次因子
            minutes_diff = (current_time - self.last_alpha_time).total_seconds() / 60
            if minutes_diff < 30:
                return False
        elif '15m' in interval or '30m' in interval:  # 15或30分钟线
            # 对于15或30分钟线，每小时更新一次因子
            minutes_diff = (current_time - self.last_alpha_time).total_seconds() / 60
            if minutes_diff < 60:
                return False
        elif 'h' in interval:  # 小时线
            # 对于小时线，每4小时更新一次因子
            hours_diff = (current_time - self.last_alpha_time).total_seconds() / 3600
            if hours_diff < 4:
                return False
        
        return True

    def _calculate_combined_alpha(self):
        """计算综合Alpha因子值"""
        if not self.factor_values:
            raise ValueError("因子数据为空，无法计算Alpha值。请确认因子数据已正确获取。")
        
        # 标准化处理各个因子值
        normalized_factors = {}
        for factor, value in self.factor_values.items():
            normalized_factors[factor] = self._normalize_factor_value(value)
        
        # 按权重加权平均
        combined = 0
        factor_contributions = []  # 新增: 记录每个因子的贡献
    
        for i, factor in enumerate(self.alpha_factors):
            if factor in normalized_factors:
                contribution = normalized_factors[factor] * self.factor_weights[i]
                combined += contribution
                # 新增: 记录详细贡献
                factor_contributions.append(f"{factor}: {contribution:.4f}")
    
        # 新增: 记录每个因子对综合alpha的贡献
        self.write_log(f"各因子对综合Alpha的贡献:\n" + "\n".join(factor_contributions))
        self.write_log(f"综合Alpha值: {combined:.6f} vs 阈值: {self.threshold}")
    
        # 更新历史记录
        self.combined_alpha_history.append(combined)
    
        return combined

    def _execute_trading_strategy(self, bar):
        """执行交易策略"""
        # 获取账户资金
        account = self.cta_engine.main_engine.get_account(self.vt_symbol.split(".")[1])
        if not account:
            self.write_log("无法获取账户信息，使用默认手数")
            capital = 100000  # 默认资金
        else:
            capital = account.balance  # 账户余额
        
        # 计算波动率 - 使用20日ATR
        atr_value = self.am.atr(20, array=False)
        if not atr_value:
            atr_value = bar.close_price * 0.01  # 默认波动率为价格的1%
        
        # 计算风险敞口 - 投入资金的2%
        risk_amount = capital * self.risk_percent
        
        # 计算交易手数 (向下取整确保不超过风险限制)
        if atr_value > 0:
            lot_size = int(risk_amount / (atr_value * self.atr_multiplier))
            lot_size = max(1, min(5, lot_size))  # 确保手数在1-5之间
        else:
            lot_size = 1
            
        # 设置限价单价格（买入略低于市价，卖出略高于市价）
        buy_price = bar.close_price * 0.998  # 市价下方0.2%
        sell_price = bar.close_price * 1.002  # 市价上方0.2%
            
        # 执行交易信号
        self._place_orders(bar, lot_size, buy_price, sell_price)
                
        # 设置止损
        self._set_stop_loss(bar, atr_value)

    def _place_orders(self, bar, lot_size, buy_price, sell_price):
        """下单逻辑"""
        if self.signal == 1:  # 买入信号
            if self.pos == 0:
                self.write_log(f"Alpha信号: {self.combined_alpha:.4f} > {self.threshold}, 买入 {lot_size}手，限价: {buy_price:.2f}")
                self.buy(buy_price, lot_size)
            elif self.pos < 0:
                self.write_log(f"Alpha信号: {self.combined_alpha:.4f} > {self.threshold}, 平空做多")
                self.cover(bar.close_price, abs(self.pos))  # 平空使用市价以确保执行
                self.buy(buy_price, lot_size)
        else:  # 卖出信号
            if self.pos == 0:
                self.write_log(f"Alpha信号: {self.combined_alpha:.4f} <= {self.threshold}, 做空 {lot_size}手，限价: {sell_price:.2f}")
                self.short(sell_price, lot_size)
            elif self.pos > 0:
                self.write_log(f"Alpha信号: {self.combined_alpha:.4f} <= {self.threshold}, 平多做空")
                self.sell(bar.close_price, abs(self.pos))  # 平多使用市价以确保执行
                self.short(sell_price, lot_size)

    def _set_stop_loss(self, bar, atr_value):
        """设置止损单"""
        if self.pos > 0:
            stop_price = bar.close_price - atr_value * self.atr_multiplier
            self.write_log(f"设置多头止损价: {stop_price:.2f}")
            self.sell(stop_price, abs(self.pos), stop=True)
        elif self.pos < 0:
            stop_price = bar.close_price + atr_value * self.atr_multiplier
            self.write_log(f"设置空头止损价: {stop_price:.2f}")
            self.cover(stop_price, abs(self.pos), stop=True)

    def _fetch_alpha_factors(self):
        """获取所选Alpha因子数据"""
        try:
            # 准备获取因子数据需要的OHLCV数据
            bars = self.get_history_data(self.lookback_days)
            if not bars:
                raise ValueError("无法获取足够的历史数据来计算Alpha因子")
                
            # 转换为DataFrame并处理数据
            df_data = self._prepare_ohlcv_dataframe(bars)
            
            # 处理缺失值
            if hasattr(self.alpha_module, 'handle_missing_values'):
                df_data = self.alpha_module.handle_missing_values(df_data)
            else:
                # 自行处理缺失值
                df_data = df_data.fillna(method='ffill').fillna(method='bfill').fillna(0)
                self.write_log("未找到handle_missing_values函数，使用默认方法处理缺失值")
            
            # 创建Alphas实例并计算因子
            alphas = self.alpha_module.Alphas(df_data)
            
            # 计算所选因子
            self._calculate_selected_factors(alphas)
            
        except Exception as e:
            self.write_log(f"获取Alpha因子失败: {e}")
            import traceback
            self.write_log(traceback.format_exc())
            return False
    
        return True

    def _prepare_ohlcv_dataframe(self, bars):
        """准备OHLCV数据框"""
        df_data = pd.DataFrame({
            'S_DQ_OPEN': [bar.open_price for bar in bars],
            'S_DQ_HIGH': [bar.high_price for bar in bars],
            'S_DQ_LOW': [bar.low_price for bar in bars],
            'S_DQ_CLOSE': [bar.close_price for bar in bars],
            'S_DQ_VOLUME': [bar.volume for bar in bars],
            'S_DQ_AMOUNT': [bar.turnover if hasattr(bar, 'turnover') and bar.turnover else 
                            bar.volume * bar.close_price for bar in bars],
        })
        
        # 检查数据有效性
        if df_data.isnull().values.any():
            missing_info = df_data.isnull().sum()
            self.write_log(f"OHLCV数据中包含缺失值，将进行填充: {missing_info[missing_info > 0].to_dict()}")
        
        # 计算百分比变化
        df_data['S_DQ_PCTCHANGE'] = df_data['S_DQ_CLOSE'].pct_change().fillna(0)
        
        # 确保数据足够进行计算
        if len(df_data) < 10:  # 大多数因子需要至少10天数据
            raise ValueError(f"历史数据不足 ({len(df_data)}条)，无法准确计算因子")
            
        return df_data

    def _calculate_selected_factors(self, alphas):
        """计算选定的因子"""
        successful_factors = 0
        factor_results = {}  # 新增: 临时存储所有因子计算结果
        
        # 先打印所有可用因子列表
        available_factors = [method for method in dir(alphas) if callable(getattr(alphas, method)) and not method.startswith('_')]
        self.write_log(f"Alpha模块提供的因子列表: {len(available_factors)}个因子")
        
        # 新增: 详细展示可用因子
        self.write_log(f"可用因子: {', '.join(sorted(available_factors))}")
        
        for factor in self.alpha_factors:
            try:
                # 直接使用因子名称作为方法名
                if hasattr(alphas, factor):
                    self.write_log(f"开始计算因子 {factor}")
                    factor_series = getattr(alphas, factor)()
                    
                    # 检查计算结果是否有效
                    if isinstance(factor_series, pd.Series) and not factor_series.empty and factor_series.iloc[-1] is not None:
                        factor_value = factor_series.iloc[-1]  # 获取最新一天的因子值
                        
                        # 检查因子值是否为有效数值
                        if np.isfinite(factor_value):  # 排除NaN, inf等无效值
                            self.factor_values[factor] = factor_value
                            factor_results[factor] = factor_value  # 新增: 存储到结果字典
                            successful_factors += 1
                            self.write_log(f"因子{factor}计算成功: {factor_value:.6f}")
                        else:
                            self.write_log(f"因子{factor}计算结果为无效值: {factor_value}")
                    else:
                        self.write_log(f"因子{factor}计算结果为空或无效: {type(factor_series)}")
                else:
                    self.write_log(f"因子{factor}不存在于factor_alpha101模块中")
            except Exception as e:
                self.write_log(f"计算因子{factor}时出错: {str(e)}")
                import traceback
                self.write_log(traceback.format_exc())
    
        # 检查是否有成功计算的因子
        if successful_factors == 0:
            raise ValueError("所有因子计算失败，无法生成交易信号")
        else:
            # 使用标准库直接导入，避免ImportError
            from datetime import datetime
            self.last_alpha_time = datetime.now()
            
            # 新增: 展示因子计算详情
            factor_detail = "\n".join([f"{factor}: {value:.6f}" for factor, value in sorted(factor_results.items())])
            self.write_log(f"因子计算详情:\n{factor_detail}")
            
            # 新增: 根据权重显示各因子贡献
            weighted_details = []
            for i, factor in enumerate(self.alpha_factors):
                if factor in factor_results:
                    # 使用标准化方法
                    normalized = self._normalize_factor_value(factor_results[factor])
                    # 计算权重贡献
                    contribution = normalized * self.factor_weights[i]
                    weighted_details.append(f"{factor}: 原值={factor_results[factor]:.6f}, 标准化={normalized:.4f}, 权重={self.factor_weights[i]:.4f}, 贡献={contribution:.4f}")
            
            self.write_log(f"因子权重贡献详情:\n" + "\n".join(weighted_details))
            self.write_log(f"成功计算{successful_factors}个Alpha因子")
            
        return successful_factors > 0  # 返回是否成功计算了因子

    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        self.put_event()

    def on_trade(self, trade: TradeData):
        """成交回报回调"""
        direction_text = '买入' if trade.direction == '多' else '卖出'
        self.write_log(f"{direction_text}{trade.volume}手，价格:{trade.price}")

        # 记录当前因子值与交易关系
        self.write_log("\n--- 交易时因子详情 ---")
        for factor, value in sorted(self.factor_values.items()):
            # 使用标准化方法
            normalized = self._normalize_factor_value(value)
            weight = self.factor_weights[self.alpha_factors.index(factor)] if factor in self.alpha_factors else 0
            contribution = normalized * weight
            self.write_log(f"{factor}: 原始值={value:.6f}, 标准化={normalized:.4f}, 权重={weight:.4f}, 贡献={contribution:.4f}")
        
        self.write_log(f"综合Alpha: {self.combined_alpha:.6f} (阈值: {self.threshold})")
        self.write_log(f"交易信号: {self.signal} (1-买入, 0-卖出)")
        self.write_log("--- 交易详情结束 ---\n")

        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """停止单状态更新回调"""
        self.put_event()
        
    def get_history_data(self, days):
        """获取历史K线数据"""
        # 对于分钟级数据，计算需要的K线条数
        if hasattr(self, 'interval') and self.interval != '1d':
            bars_needed = self._calculate_lookback_bars()
        else:
            bars_needed = days
            
        self.write_log(f"尝试获取{bars_needed}条历史K线数据")
        
        # 尝试使用数组管理器中的数据
        if self.am.inited:
            bars = self._get_data_from_array_manager(bars_needed)
            if bars:
                return bars
        
        # 尝试从回测引擎获取数据
        bars = self._get_data_from_backtest_engine(days)  # 回测引擎仍使用天数
        if bars:
            return bars
        
        # 如果以上方法都失败
        self.write_log("无法获取历史数据，将使用模拟数据")
        return []
    
    def _get_data_from_array_manager(self, days):
        """从数组管理器获取历史数据"""
        bars = []
        for i in range(min(days, len(self.am.close_array))):
            idx = -(i + 1)
            # 创建BarData对象
            from datetime import datetime, timedelta
            bar = BarData(
                gateway_name="BackTest",
                symbol=self.vt_symbol.split(".")[0],
                exchange=self.vt_symbol.split(".")[1],
                datetime=datetime.now() - timedelta(days=i+1),  # 模拟日期
                interval="1d",  # 日线
                volume=self.am.volume_array[idx],
                open_price=self.am.open_array[idx],
                high_price=self.am.high_array[idx],
                low_price=self.am.low_array[idx],
                close_price=self.am.close_array[idx],
                turnover=0  # ArrayManager可能没有turnover数据
            )
            bars.append(bar)
        
        if bars:
            bars = bars[::-1]  # 最后一次性反转
            self.write_log(f"从ArrayManager获取了{len(bars)}条历史数据")
            
        return bars
    
    def _get_data_from_backtest_engine(self, days):
        """从回测引擎获取历史数据"""
        if hasattr(self, 'cta_engine') and hasattr(self.cta_engine, 'query_bar_history'):
            try:
                from datetime import datetime, timedelta
                end = datetime.now()
                start = end - timedelta(days=days)
                history_data = self.cta_engine.query_bar_history(self.vt_symbol, start, end)
                if history_data:
                    self.write_log(f"从回测引擎获取了{len(history_data)}条历史数据")
                    return history_data
            except Exception as e:
                self.write_log(f"从回测引擎获取历史数据失败: {e}")
        
        return []
    
    def _print_factor_trends(self):
        """打印因子趋势"""
        if not self.datetime_history:
            return
            
        self.write_log("\n--- 因子趋势摘要 (最近5个数据点) ---")
        
        # 获取最近5个记录点
        n = min(5, len(self.datetime_history))
        recent_dates = self.datetime_history[-n:]
        
        # 打印日期头
        date_header = " " * 10
        for dt in recent_dates:
            date_header += f"{dt.strftime('%m-%d')} "
        self.write_log(date_header)
        
        # 打印每个因子的趋势
        for factor in self.alpha_factors:
            if factor in self.factor_history and len(self.factor_history[factor]) >= n:
                trend_line = f"{factor:10s}"
                for val in self.factor_history[factor][-n:]:
                    trend_line += f"{val:7.2f} "
                self.write_log(trend_line)
        
        # 打印综合Alpha趋势
        if len(self.combined_alpha_history) >= n:
            trend_line = f"{'Combined':10s}"
            for val in self.combined_alpha_history[-n:]:
                trend_line += f"{val:7.2f} "
            self.write_log(trend_line)
        
        # 打印信号趋势
        if len(self.signal_history) >= n:
            trend_line = f"{'Signal':10s}"
            for val in self.signal_history[-n:]:
                trend_line += f"{val:7d} "
            self.write_log(trend_line)
        
        self.write_log("--- 因子趋势摘要结束 ---\n")
    
    def _generate_factor_summary(self):
        """生成因子表现总结"""
        if not self.datetime_history:
            self.write_log("没有足够的历史数据生成因子总结")
            return
            
        self.write_log("\n=== 因子表现总结 ===")
        self.write_log(f"分析周期: {self.datetime_history[0]} 至 {self.datetime_history[-1]}")
        self.write_log(f"数据点数: {len(self.datetime_history)}")
        
        # 总结每个因子的统计特性
        for factor in self.alpha_factors:
            if factor in self.factor_history and self.factor_history[factor]:
                values = self.factor_history[factor]
                self.write_log(f"\n因子 {factor} 统计特性:")
                self.write_log(f"  平均值: {np.mean(values):.6f}")
                self.write_log(f"  标准差: {np.std(values):.6f}")
                self.write_log(f"  最大值: {np.max(values):.6f}")
                self.write_log(f"  最小值: {np.min(values):.6f}")
                self.write_log(f"  中位数: {np.median(values):.6f}")
                
                # 计算与综合Alpha的相关性
                if len(values) == len(self.combined_alpha_history):
                    corr = np.corrcoef(values, self.combined_alpha_history)[0, 1]
                    self.write_log(f"  与综合Alpha相关性: {corr:.4f}")
        
        # 综合Alpha统计特性
        if self.combined_alpha_history:
            self.write_log(f"\n综合Alpha统计特性:")
            self.write_log(f"  平均值: {np.mean(self.combined_alpha_history):.6f}")
            self.write_log(f"  标准差: {np.std(self.combined_alpha_history):.6f}")
            self.write_log(f"  最大值: {np.max(self.combined_alpha_history):.6f}")
            self.write_log(f"  最小值: {np.min(self.combined_alpha_history):.6f}")
            
            # 信号分布
            if self.signal_history:
                buy_count = sum(1 for s in self.signal_history if s == 1)
                sell_count = len(self.signal_history) - buy_count
                self.write_log(f"\n信号分布:")
                self.write_log(f"  买入信号: {buy_count} ({buy_count/len(self.signal_history)*100:.2f}%)")
                self.write_log(f"  卖出信号: {sell_count} ({sell_count/len(self.signal_history)*100:.2f}%)")
        
        self.write_log("\n=== 因子表现总结结束 ===")
    
    def evaluate_factor_accuracy(self):
        """评估因子预测的正确率"""
        if len(self.datetime_history) < 2 or not self.factor_history:
            self.write_log("历史数据不足，无法评估因子预测正确率")
            return
            
        self.write_log("\n===== 因子预测正确率评估 =====")
        
        # 获取历史K线数据作为价格参考
        price_changes = []
        dates = []
        
        # 使用ArrayManager中的收盘价计算价格变动
        for i in range(1, min(len(self.datetime_history), len(self.am.close_array))):
            idx = -i
            prev_idx = -(i+1)
            if abs(prev_idx) < len(self.am.close_array):  # 确保索引在范围内
                # 计算日收益率
                price_change = (self.am.close_array[idx] / self.am.close_array[prev_idx]) - 1
                price_changes.append(price_change)
                dates.append(self.datetime_history[-i])
        
        price_changes.reverse()  # 时间正序排列
        dates.reverse()
        
        # 针对每个因子评估预测能力
        for factor in self.alpha_factors:
            if factor not in self.factor_history or len(self.factor_history[factor]) < 2:
                self.write_log(f"因子 {factor} 历史数据不足，跳过评估")
                continue
                
            self.write_log(f"\n--- 因子 {factor} 预测评估 ---")
            
            # 仅使用与价格变动数据对应的因子数据
            factor_values = self.factor_history[factor][-len(price_changes):]
            if len(factor_values) < 2:
                self.write_log(f"因子 {factor} 有效数据点不足，跳过评估")
                continue
            
            # 计算因子与下一期收益的相关性
            try:
                # 错开一期，计算因子与下一期收益的相关系数
                next_day_corr = np.corrcoef(factor_values[:-1], price_changes[1:])[0, 1]
                self.write_log(f"与下一期收益相关系数: {next_day_corr:.4f}")
                
                # 方向性准确率：因子值符号与下期价格变动方向一致的比例
                correct_direction = 0
                for i in range(len(factor_values) - 1):
                    factor_sign = 1 if factor_values[i] > 0 else -1
                    price_sign = 1 if price_changes[i+1] > 0 else -1
                    if factor_sign == price_sign:
                        correct_direction += 1
                
                direction_accuracy = correct_direction / (len(factor_values) - 1)
                self.write_log(f"方向预测准确率: {direction_accuracy:.4f} ({correct_direction}/{len(factor_values)-1})")
                
                # 评估不同因子值区间的预测能力
                strong_positive = []
                weak_positive = []
                neutral = []
                weak_negative = []
                strong_negative = []
                
                for i in range(len(factor_values) - 1):
                    norm_value = self._normalize_factor_value(factor_values[i])
                    ret = price_changes[i+1]
                    
                    if norm_value > 0.7:  # 强烈看涨信号
                        strong_positive.append(ret)
                    elif norm_value > 0.3:  # 弱看涨信号
                        weak_positive.append(ret)
                    elif norm_value < -0.7:  # 强烈看跌信号
                        strong_negative.append(ret)
                    elif norm_value < -0.3:  # 弱看跌信号
                        weak_negative.append(ret)
                    else:  # 中性信号
                        neutral.append(ret)
                
                # 计算各类信号的平均收益率和胜率
                self._evaluate_signal_group("强看涨", strong_positive)
                self._evaluate_signal_group("弱看涨", weak_positive)
                self._evaluate_signal_group("中性", neutral)
                self._evaluate_signal_group("弱看跌", weak_negative)
                self._evaluate_signal_group("强看跌", strong_negative)
            
            except Exception as e:
                self.write_log(f"评估因子 {factor} 时出错: {str(e)}")
                import traceback
                self.write_log(traceback.format_exc())
        
        # 评估综合Alpha信号的预测能力
        if len(self.combined_alpha_history) >= len(price_changes):
            self.write_log("\n--- 综合Alpha信号预测评估 ---")
            try:
                alpha_values = self.combined_alpha_history[-len(price_changes):]
                
                # 计算信号与下一期收益的相关性
                next_day_corr = np.corrcoef(alpha_values[:-1], price_changes[1:])[0, 1]
                self.write_log(f"与下一期收益相关系数: {next_day_corr:.4f}")
                
                # 计算基于阈值的交易表现
                trade_correct = 0
                for i in range(len(alpha_values) - 1):
                    signal = 1 if alpha_values[i] > self.threshold else 0
                    price_direction = 1 if price_changes[i+1] > 0 else 0
                    if signal == price_direction:
                        trade_correct += 1
                
                trading_accuracy = trade_correct / (len(alpha_values) - 1)
                self.write_log(f"交易信号准确率: {trading_accuracy:.4f} ({trade_correct}/{len(alpha_values)-1})")
                
                # 计算平均交易收益
                long_returns = [price_changes[i+1] for i in range(len(alpha_values) - 1) if alpha_values[i] > self.threshold]
                short_returns = [-price_changes[i+1] for i in range(len(alpha_values) - 1) if alpha_values[i] <= self.threshold]
                
                if long_returns:
                    avg_long_return = np.mean(long_returns)
                    win_rate_long = sum(1 for ret in long_returns if ret > 0) / len(long_returns)
                    self.write_log(f"多头平均收益: {avg_long_return:.4%}, 胜率: {win_rate_long:.4f}")
                
                if short_returns:
                    avg_short_return = np.mean(short_returns)
                    win_rate_short = sum(1 for ret in short_returns if ret > 0) / len(short_returns)
                    self.write_log(f"空头平均收益: {avg_short_return:.4%}, 胜率: {win_rate_short:.4f}")
            
            except Exception as e:
                self.write_log(f"评估综合Alpha信号时出错: {str(e)}")
        
        self.write_log("===== 因子预测评估结束 =====\n")
    
    def _evaluate_signal_group(self, name, returns):
        """评估特定信号组的表现"""
        if not returns:
            self.write_log(f"  {name}信号: 无样本")
            return
            
        avg_return = np.mean(returns)
        win_rate = sum(1 for ret in returns if ret > 0) / len(returns)
        self.write_log(f"  {name}信号: 样本数={len(returns)}, 平均收益={avg_return:.4%}, 胜率={win_rate:.4f}")

    def _calculate_lookback_bars(self):
        """根据K线周期计算所需的Bar数量"""
        # 获取当前K线周期
        interval = getattr(self, 'interval', '1d')  # 默认日线
        
        # 根据K线周期确定每天的bar数量
        if '1m' in interval:
            bars_per_day = 240  # 假设一天有4小时交易，每小时60分钟
        elif '5m' in interval:
            bars_per_day = 48   # 4小时 * 12个5分钟
        elif '15m' in interval:
            bars_per_day = 16   # 4小时 * 4个15分钟
        elif '30m' in interval:
            bars_per_day = 8    # 4小时 * 2个30分钟
        elif '1h' in interval or 'h' in interval:
            bars_per_day = 4    # 4小时
        else:  # 默认日线
            bars_per_day = 1
        
        # 计算需要的总bar数量，并添加10%的余量
        total_bars = int(self.lookback_days * bars_per_day * 1.1)
        
        return total_bars

    def _display_module_loading_summary(self):
        """显示模块加载的详细信息"""
        try:
            sys.stdout.write("\n==== Alpha因子模块加载详情 ====\n")
            
            if hasattr(self, 'alpha_module'):
                # 获取模块文件路径
                if hasattr(self.alpha_module, '__file__'):
                    module_path = self.alpha_module.__file__
                    sys.stdout.write(f"模块文件路径: {module_path}\n")
                else:
                    sys.stdout.write("无法获取模块文件路径\n")
                
                # 获取模块名称
                module_name = self.alpha_module.__name__ if hasattr(self.alpha_module, '__name__') else "未知"
                sys.stdout.write(f"模块名称: {module_name}\n")
                
                # 检查模块是否有Alphas类
                if hasattr(self.alpha_module, 'Alphas'):
                    sys.stdout.write("模块包含Alphas类: [OK]\n")
                    
                    # 获取可用因子列表
                    alphas_class = getattr(self.alpha_module, 'Alphas')
                    alpha_methods = [m for m in dir(alphas_class) if callable(getattr(alphas_class, m)) and not m.startswith('_')]
                    sys.stdout.write(f"可用因子数量: {len(alpha_methods)}\n")
                    
                    # 检查策略使用的因子是否存在
                    available_factors = []
                    missing_factors = []
                    for factor in self.alpha_factors:
                        if factor in alpha_methods:
                            available_factors.append(factor)
                        else:
                            missing_factors.append(factor)
                    
                    if available_factors:
                        sys.stdout.write(f"找到策略所需因子: {', '.join(available_factors)}\n")
                    if missing_factors:
                        sys.stdout.write(f"警告: 未找到策略所需因子: {', '.join(missing_factors)}\n")
                else:
                    sys.stdout.write("警告: 模块不包含Alphas类\n")
            else:
                sys.stdout.write("警告: Alpha模块未成功加载\n")
                
            sys.stdout.write("============================\n\n")
            sys.stdout.flush()
        except Exception as e:
            sys.stdout.write(f"显示模块加载详情时发生错误: {e}\n")
            sys.stdout.flush()
    
    def save_factor_data_to_csv(self):
        """将因子数据保存为CSV文件以便于后续分析和绘图"""
        try:
            import os
            import pandas as pd
            import tempfile
            import traceback
            from datetime import datetime
            
            # 检查历史数据
            if not self.datetime_history:
                error_msg = "没有历史数据，无法保存因子数据"
                self.write_log(error_msg)
                self._log_to_file(error_msg) 
                sys.stdout.write(f"[ERROR] {error_msg}\n")
                sys.stdout.flush()
                return None
            
            # 打印历史数据长度
            data_length = len(self.datetime_history)
            debug_msg = f"准备保存因子数据，共有{data_length}个数据点"
            self.write_log(debug_msg)
            self._log_to_file(debug_msg)
            
            # 准备数据字典
            data_dict = {"datetime": self.datetime_history}
            self._log_to_file(f"基础时间序列: {len(data_dict['datetime'])}个数据点")
            
            # 添加每个因子的历史值
            for factor in self.alpha_factors:
                if factor in self.factor_history and self.factor_history[factor]:
                    # 确保长度匹配
                    factor_values = self.factor_history[factor]
                    self._log_to_file(f"因子{factor}有{len(factor_values)}个历史记录")
                    if len(factor_values) == len(self.datetime_history):
                        data_dict[f"{factor}"] = factor_values
                        self._log_to_file(f"添加因子{factor}数据成功")
                    else:
                        self._log_to_file(f"警告：因子{factor}数据长度({len(factor_values)})与时间序列({len(self.datetime_history)})不匹配")
            
            # 添加综合Alpha值
            if len(self.combined_alpha_history) == len(self.datetime_history):
                data_dict["combined_alpha"] = self.combined_alpha_history
                self._log_to_file(f"添加综合Alpha数据成功，共{len(self.combined_alpha_history)}个数据点")
            else:
                self._log_to_file(f"警告：综合Alpha数据长度({len(self.combined_alpha_history)})与时间序列({len(self.datetime_history)})不匹配")
            
            # 添加交易信号
            if len(self.signal_history) == len(self.datetime_history):
                data_dict["signal"] = self.signal_history
                self._log_to_file(f"添加信号数据成功，共{len(self.signal_history)}个数据点")
            else:
                self._log_to_file(f"警告：信号数据长度({len(self.signal_history)})与时间序列({len(self.datetime_history)})不匹配")
            
            # 创建DataFrame
            try:
                df = pd.DataFrame(data_dict)
                self._log_to_file(f"成功创建DataFrame，形状: {df.shape}")
            except Exception as df_err:
                error_msg = f"创建DataFrame失败: {df_err}"
                self.write_log(error_msg)
                self._log_to_file(error_msg)
                sys.stdout.write(f"[ERROR] {error_msg}\n")
                sys.stdout.flush()
                return None
                
            # 确保目录存在，首先尝试当前工作目录
            current_dir = os.getcwd()
            self._log_to_file(f"当前工作目录: {current_dir}")
            
            # 尝试在当前目录直接创建CSV文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"alpha_factors_{timestamp}.csv"
            file_path = os.path.join(current_dir, file_name)
            
            self._log_to_file(f"尝试直接写入文件: {file_path}")
            sys.stdout.write(f"准备保存CSV文件: {file_path}\n")
            sys.stdout.flush()
                
            # 保存到CSV，先尝试直接保存
            try:
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                
                # 验证文件是否成功创建
                if os.path.exists(file_path):
                    filesize = os.path.getsize(file_path)
                    success_msg = f"因子数据已成功保存至: {file_path} (文件大小: {filesize} 字节)"
                    self.write_log(success_msg)
                    self._log_to_file(success_msg)
                    sys.stdout.write(f"\n[成功] {success_msg}\n")
                    sys.stdout.flush()
                    return file_path
                else:
                    self._log_to_file("文件写入后无法验证文件存在性")
            except Exception as direct_save_err:
                self._log_to_file(f"直接保存到当前目录失败: {direct_save_err}")
                self._log_to_file(traceback.format_exc())
            
            # 如果直接保存失败，尝试其他位置
            save_locations = [
                os.path.join(current_dir, "factor_data"),
                os.path.join(current_dir, "factor_reports"),
                os.path.join(current_dir, "logs")
            ]
            
            for location in save_locations:
                try:
                    # 确保目录存在
                    self._log_to_file(f"尝试保存到: {location}")
                    if not os.path.exists(location):
                        os.makedirs(location)
                        self._log_to_file(f"创建目录: {location}")
                    
                    # 尝试保存
                    backup_path = os.path.join(location, file_name)
                    self._log_to_file(f"尝试保存到备用位置: {backup_path}")
                    
                    df.to_csv(backup_path, index=False, encoding="utf-8-sig")
                    
                    # 验证文件
                    if os.path.exists(backup_path):
                        filesize = os.path.getsize(backup_path)
                        success_msg = f"因子数据已成功保存至备用位置: {backup_path} (文件大小: {filesize} 字节)"
                        self.write_log(success_msg)
                        self._log_to_file(success_msg)
                        sys.stdout.write(f"\n[成功] {success_msg}\n")
                        sys.stdout.flush()
                        return backup_path
                except Exception as backup_err:
                    self._log_to_file(f"保存到备用位置 {location} 失败: {backup_err}")
            
            # 所有保存尝试都失败
            error_msg = "所有保存尝试均失败，无法保存CSV文件"
            self.write_log(error_msg)
            self._log_to_file(error_msg)
            sys.stdout.write(f"[ERROR] {error_msg}\n")
            sys.stdout.flush()
            return None
            
        except Exception as e:
            error_msg = f"保存因子数据过程中发生未预期错误: {e}"
            self.write_log(error_msg)
            self._log_to_file(error_msg)
            self._log_to_file(traceback.format_exc())
            sys.stdout.write(f"[ERROR] {error_msg}\n")
            sys.stdout.flush()
        
    def generate_factor_report(self):
        """生成简单的因子趋势报告，输出到日志文件中"""
        if not self.datetime_history:
            self.write_log("没有足够的数据生成因子报告")
            return
        
        self.write_log("\n========== 因子趋势综合报告 ==========")
        self.write_log(f"报告生成时间: {datetime.now()}")
        self.write_log(f"分析周期: {self.datetime_history[0]} 至 {self.datetime_history[-1]}")
        self.write_log(f"数据点数量: {len(self.datetime_history)}")
        
        # 计算每个因子的最新趋势
        self.write_log("\n--- 最近因子走势分析 ---")
        for factor in self.alpha_factors:
            if factor in self.factor_history and len(self.factor_history[factor]) >= 3:
                values = self.factor_history[factor]
                # 计算最近3个周期的变化趋势
                recent_trend = ""
                if values[-1] > values[-2] and values[-2] > values[-3]:
                    recent_trend = "持续上升"
                elif values[-1] < values[-2] and values[-2] < values[-3]:
                    recent_trend = "持续下降"
                elif values[-1] > values[-2]:
                    recent_trend = "近期上升"
                elif values[-1] < values[-2]:
                    recent_trend = "近期下降"
                else:
                    recent_trend = "近期持平"
                
                # 计算因子有效性指标
                if len(values) >= 5:
                    # 计算波动性
                    volatility = np.std(values[-5:])
                    avg_value = np.mean(values[-5:])
                    cv = volatility / abs(avg_value) if abs(avg_value) > 1e-10 else 0
                    
                    # 计算与其他因子的相关性
                    correlations = []
                    for other_factor in self.alpha_factors:
                        if other_factor != factor and other_factor in self.factor_history and len(self.factor_history[other_factor]) >= 5:
                            other_values = self.factor_history[other_factor][-5:]
                            if len(other_values) == len(values[-5:]):
                                corr = np.corrcoef(values[-5:], other_values)[0, 1]
                                correlations.append((other_factor, corr))
                    
                    # 输出因子分析
                    self.write_log(f"\n因子 {factor}:")
                    self.write_log(f"  最新值: {values[-1]:.6f}")
                    self.write_log(f"  走势: {recent_trend}")
                    self.write_log(f"  波动率: {volatility:.6f} (变异系数: {cv:.4f})")
                    
                    if correlations:
                        self.write_log("  与其他因子相关性:")
                        for other_factor, corr in sorted(correlations, key=lambda x: abs(x[1]), reverse=True):
                            correlation_str = f"    {other_factor}: {corr:.4f}"
                            if abs(corr) > 0.7:
                                correlation_str += " (高相关)"
                            self.write_log(correlation_str)
        
        # 评估信号有效性
        self.write_log("\n--- 交易信号评估 ---")
        if len(self.signal_history) >= 3:
            # 分析信号变化频率
            signal_changes = sum(1 for i in range(1, len(self.signal_history)) if self.signal_history[i] != self.signal_history[i-1])
            change_rate = signal_changes / (len(self.signal_history) - 1)
            
            self.write_log(f"信号变化次数: {signal_changes}")
            self.write_log(f"信号变化率: {change_rate:.4f}")
            
            if change_rate > 0.5:
                self.write_log("信号频繁变化，建议增加阈值或调整因子权重以提高稳定性")
            elif change_rate < 0.1:
                self.write_log("信号变化较少，可能对市场变化不敏感，建议降低阈值或调整因子权重")
        
        # 最新的交易建议
        if self.combined_alpha_history:
            latest_alpha = self.combined_alpha_history[-1]
            signal = 1 if latest_alpha > self.threshold else 0
            
            self.write_log("\n--- 最新交易建议 ---")
            self.write_log(f"最新综合Alpha值: {latest_alpha:.6f} (阈值: {self.threshold})")
            if signal == 1:
                self.write_log("交易建议: 买入")
            else:
                self.write_log("交易建议: 卖出")
            
            # 计算综合Alpha趋势强度
            if len(self.combined_alpha_history) >= 3:
                alpha_trend = np.mean(np.diff(self.combined_alpha_history[-3:]))
                alpha_trend_str = "强烈上升" if alpha_trend > 0.1 else "上升" if alpha_trend > 0.01 else "微弱上升" if alpha_trend > 0 else "强烈下降" if alpha_trend < -0.1 else "下降" if alpha_trend < -0.01 else "微弱下降" if alpha_trend < 0 else "稳定"
                self.write_log(f"综合Alpha趋势: {alpha_trend_str} (变化率: {alpha_trend:.6f})")
        
        self.write_log("\n========== 因子报告结束 ==========")
        
        # 提醒用户保存了CSV文件
        csv_path = self.save_factor_data_to_csv()
        if csv_path:
            self.write_log(f"\n已生成CSV数据文件: {csv_path}")
            self.write_log("您可以使用Excel等工具打开该文件进行分析和绘图")
    
    def generate_html_report(self):
        """生成HTML格式的因子分析报告"""
        try:
            import os
            import pandas as pd
            from datetime import datetime
            
            if not self.datetime_history:
                self.write_log("没有历史数据，无法生成HTML报告")
                return None
                
            # 准备数据
            data_dict = {"datetime": self.datetime_history}
            
            # 添加每个因子的历史值
            for factor in self.alpha_factors:
                if factor in self.factor_history and self.factor_history[factor]:
                    # 确保长度匹配
                    factor_values = self.factor_history[factor]
                    if len(factor_values) == len(self.datetime_history):
                        data_dict[f"{factor}"] = factor_values
            
            # 添加综合Alpha值
            if len(self.combined_alpha_history) == len(self.datetime_history):
                data_dict["combined_alpha"] = self.combined_alpha_history
            
            # 添加交易信号
            if len(self.signal_history) == len(self.datetime_history):
                data_dict["signal"] = self.signal_history
            
            # 创建DataFrame
            df = pd.DataFrame(data_dict)
            
            # 创建保存目录，使用当前工作目录而不是硬编码路径
            options = [
                os.path.join(os.getcwd(), "factor_reports"),
                os.path.join(os.path.dirname(os.getcwd()), "factor_reports"),
                os.path.join(os.path.expanduser("~"), "factor_reports")
            ]
            
            # 尝试多个路径，选择第一个可写入的
            save_dir = None
            for dir_option in options:
                self.write_log(f"尝试使用HTML报告目录: {dir_option}")
                if self._test_directory_writeable(dir_option):
                    save_dir = dir_option
                    self.write_log(f"已选择可写入HTML报告目录: {save_dir}")
                    break
            
            # 如果所有尝试都失败，使用临时目录
            if not save_dir:
                self.write_log("所有目录都不可写入，使用系统临时目录")
                import tempfile
                save_dir = tempfile.gettempdir()
                self.write_log(f"将使用系统临时目录: {save_dir}")
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join(save_dir, f"alpha_report_{timestamp}.html")
            self.write_log(f"将创建HTML报告: {file_path}")
            
            # 生成HTML报告头部
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>Alpha因子分析报告</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    h1, h2, h3 { color: #333; }
                    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    tr:nth-child(even) { background-color: #f9f9f9; }
                    .chart-container { width: 100%; height: 400px; margin-bottom: 20px; }
                    .positive { color: green; }
                    .negative { color: red; }
                    .signal-buy { background-color: #d4edda; }
                    .signal-sell { background-color: #f8d7da; }
                </style>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
            </head>
            <body>
                <div class="container">
                    <h1>Alpha因子分析报告</h1>
                    <p>报告生成时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                    
                    <h2>策略参数</h2>
                    <table>
                        <tr><th>参数</th><th>值</th></tr>
                        <tr><td>因子列表</td><td>""" + str(self.alpha_factors) + """</td></tr>
                        <tr><td>因子权重</td><td>""" + str(self.factor_weights) + """</td></tr>
                        <tr><td>信号阈值</td><td>""" + str(self.threshold) + """</td></tr>
                        <tr><td>回看天数</td><td>""" + str(self.lookback_days) + """</td></tr>
                    </table>
                    
                    <h2>因子趋势图</h2>
                    <div id="factorChart" class="chart-container"></div>
                    
                    <h2>综合Alpha趋势图</h2>
                    <div id="alphaChart" class="chart-container"></div>
                    
                    <h2>交易信号</h2>
                    <div id="signalChart" class="chart-container"></div>
                    
                    <h2>因子数据表</h2>
                    <div style="overflow-x: auto;">
                        <table id="dataTable">
                            <thead>
                                <tr>
                                    <th>日期</th>
            """
            
            # 添加表头
            for factor in self.alpha_factors:
                if factor in self.factor_history:
                    html_content += f"<th>{factor}</th>"
            
            html_content += "<th>综合Alpha</th><th>交易信号</th></tr></thead><tbody>"
            
            # 添加数据行
            date_format = "%Y-%m-%d %H:%M:%S" if "m" in getattr(self, 'interval', '1d') else "%Y-%m-%d"
            for i in range(len(df)):
                row = df.iloc[i]
                signal_class = "signal-buy" if row.get("signal", 0) == 1 else "signal-sell"
                
                html_content += f"<tr class='{signal_class}'>"
                html_content += f"<td>{row['datetime'].strftime(date_format)}</td>"
                
                for factor in self.alpha_factors:
                    if factor in self.factor_history:
                        value = row.get(factor, "")
                        value_class = "positive" if value > 0 else "negative" if value < 0 else ""
                        html_content += f"<td class='{value_class}'>{value:.6f}</td>"
                
                # 添加综合Alpha和信号
                alpha_value = row.get("combined_alpha", "")
                alpha_class = "positive" if alpha_value > 0 else "negative" if alpha_value < 0 else ""
                signal_value = "买入" if row.get("signal", 0) == 1 else "卖出"
                
                html_content += f"<td class='{alpha_class}'>{alpha_value:.6f}</td>"
                html_content += f"<td>{signal_value}</td>"
                html_content += "</tr>"
            
            html_content += """
                            </tbody>
                        </table>
                    </div>
                    
                    <h2>因子统计分析</h2>
                    <table>
                        <tr><th>因子</th><th>平均值</th><th>标准差</th><th>最大值</th><th>最小值</th><th>与综合Alpha相关性</th></tr>
            """
            
            # 添加因子统计信息
            for factor in self.alpha_factors:
                if factor in self.factor_history and len(self.factor_history[factor]) > 0:
                    values = np.array(self.factor_history[factor])
                    mean_val = np.mean(values)
                    std_val = np.std(values)
                    max_val = np.max(values)
                    min_val = np.min(values)
                    
                    # 计算与综合Alpha的相关性
                    if len(values) == len(self.combined_alpha_history) and len(values) > 1:
                        corr = np.corrcoef(values, self.combined_alpha_history)[0, 1]
                        corr_str = f"{corr:.4f}"
                    else:
                        corr_str = "N/A"
                    
                    html_content += f"""
                        <tr>
                            <td>{factor}</td>
                            <td>{mean_val:.6f}</td>
                            <td>{std_val:.6f}</td>
                            <td>{max_val:.6f}</td>
                            <td>{min_val:.6f}</td>
                            <td>{corr_str}</td>
                        </tr>
                    """
            
            # 添加交易信号统计
            if self.signal_history:
                buy_count = sum(1 for s in self.signal_history if s == 1)
                sell_count = len(self.signal_history) - buy_count
                buy_percent = buy_count/len(self.signal_history)*100
                sell_percent = sell_count/len(self.signal_history)*100
                
                html_content += f"""
                    <tr><td colspan="6"><b>交易信号统计</b></td></tr>
                    <tr><td>买入信号</td><td colspan="5">{buy_count} ({buy_percent:.2f}%)</td></tr>
                    <tr><td>卖出信号</td><td colspan="5">{sell_count} ({sell_percent:.2f}%)</td></tr>
                """
            
            # 完成HTML并添加图表脚本
            html_content += """
                    </table>
                </div>
                
                <script>
                    // 初始化图表
                    var factorChart = echarts.init(document.getElementById('factorChart'));
                    var alphaChart = echarts.init(document.getElementById('alphaChart'));
                    var signalChart = echarts.init(document.getElementById('signalChart'));
                    
                    // 数据准备
                    var dates = [
            """
            
            # 添加日期数据
            for dt in df['datetime']:
                html_content += f"'{dt.strftime(date_format)}', "
            
            html_content += "];\n"
            
            # 添加因子数据系列
            html_content += "var factorSeries = [\n"
            for factor in self.alpha_factors:
                if factor in self.factor_history and len(self.factor_history[factor]) > 0:
                    html_content += "{\n"
                    html_content += f"name: '{factor}',\n"
                    html_content += "type: 'line',\n"
                    html_content += f"data: {str(self.factor_history[factor])},\n"
                    html_content += "},\n"
            html_content += "];\n"
            
            # 添加综合Alpha数据
            html_content += f"var alphaData = {str(self.combined_alpha_history)};\n"
                        
            
            # 添加信号数据
            html_content += f"var signalData = {str(self.signal_history)};\n"
            
            # 添加阈值数据（作为参考线）
            threshold_data = [self.threshold] * len(df)
            html_content += f"var thresholdData = {str(threshold_data)};\n"
            
            # 完成图表脚本
            html_content += """
                    // 配置因子趋势图
                    var factorOption = {
                        title: {
                            text: '各因子趋势'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: []
                        },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value'
                        },
                        dataZoom: [
                            {
                                type: 'inside',
                                start: 0,
                                end: 100
                            },
                            {
                                start: 0,
                                end: 100
                            }
                        ],
                        series: factorSeries
                    };
                    
                    // 配置综合Alpha趋势图
                    var alphaOption = {
                        title: {
                            text: '综合Alpha趋势'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value'
                        },
                        dataZoom: [
                            {
                                type: 'inside',
                                start: 0,
                                end: 100
                            },
                            {
                                start: 0,
                                end: 100
                            }
                        ],
                        series: [
                            {
                                name: '综合Alpha',
                                type: 'line',
                                data: alphaData,
                                markLine: {
                                    data: [
                                        {
                                            name: '阈值',
                                            yAxis: """ + str(self.threshold) + """,
                                            lineStyle: {
                                                color: 'red'
                                            },
                                            label: {
                                                formatter: '阈值: """ + str(self.threshold) + """'
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    };
                    
                    // 配置信号图
                    var signalOption = {
                        title: {
                            text: '交易信号'
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: function(params) {
                                var value = params[0].value;
                                return params[0].name + '<br/>' + (value == 1 ? '买入' : '卖出');
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'category',
                            data: ['卖出', '买入'],
                            inverse: true
                        },
                        dataZoom: [
                            {
                                type: 'inside',
                                start: 0,
                                end: 100
                            },
                            {
                                start: 0,
                                end: 100
                            }
                        ],
                        series: [
                            {
                                name: '信号',
                                type: 'line',
                                step: 'end',
                                data: signalData,
                                markArea: {
                                    itemStyle: {
                                        color: 'rgba(255, 173, 177, 0.4)'
                                    },
                                    data: []
                                }
                            }
                        ],
                        visualMap: {
                            show: false,
                            pieces: [
                                {
                                    min: 0,
                                    max: 0,
                                    color: '#ff8889'
                                },
                                {
                                    min: 1,
                                    max: 1,
                                    color: '#77d379'
                                }
                            ]
                        }
                    };
                    
                    // 设置图表选项并绘制
                    factorChart.setOption(factorOption);
                    alphaChart.setOption(alphaOption);
                    signalChart.setOption(signalOption);
                    
                    // 响应窗口大小变化
                    window.addEventListener('resize', function() {
                        factorChart.resize();
                        alphaChart.resize();
                        signalChart.resize();
                    });
                </script>
            </body>
            </html>
            """
            
            # 确保目录存在，首先尝试当前工作目录
            current_dir = os.getcwd()
            self._log_to_file(f"当前工作目录: {current_dir}")
            
            # 尝试在当前目录直接创建HTML文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"alpha_report_{timestamp}.html"
            direct_file_path = os.path.join(current_dir, file_name)
            
            self._log_to_file(f"尝试直接写入HTML文件: {direct_file_path}")
            sys.stdout.write(f"准备保存HTML报告: {direct_file_path}\n")
            sys.stdout.flush()
            
            # 保存HTML文件，先尝试直接保存
            try:
                with open(direct_file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                # 验证文件是否成功创建
                if os.path.exists(direct_file_path):
                    filesize = os.path.getsize(direct_file_path)
                    success_msg = f"HTML报告已成功保存至: {direct_file_path} (文件大小: {filesize} 字节)"
                    self.write_log(success_msg)
                    self._log_to_file(success_msg)
                    sys.stdout.write(f"\n[成功] HTML因子分析报告已成功生成: \n{direct_file_path}\n")
                    sys.stdout.write("请使用浏览器打开查看交互式图表和数据\n")
                    sys.stdout.flush()
                    
                    # 尝试自动打开HTML报告
                    try:
                        import webbrowser
                        self._log_to_file("尝试使用webbrowser打开HTML报告")
                        webbrowser.open(direct_file_path)
                        sys.stdout.write("已尝试自动打开浏览器查看报告\n")
                        sys.stdout.flush()
                    except Exception as browser_err:
                        self._log_to_file(f"自动打开浏览器失败: {browser_err}")
                    
                    return direct_file_path
            except Exception as direct_save_err:
                self._log_to_file(f"直接保存HTML到当前目录失败: {direct_save_err}")
                self._log_to_file(traceback.format_exc())
            
            # 如果直接保存失败，尝试其他位置
            save_locations = [
                os.path.join(current_dir, "factor_reports"),
                os.path.join(os.path.expanduser("~"), "factor_reports"),
                tempfile.gettempdir()
            ]
            
            for location in save_locations:
                try:
                    # 确保目录存在
                    self._log_to_file(f"尝试保存HTML到备用位置: {location}")
                    if not os.path.exists(location):
                        os.makedirs(location)
                        self._log_to_file(f"创建备用目录: {location}")
                    
                    # 尝试保存
                    backup_path = os.path.join(location, file_name)
                    self._log_to_file(f"尝试保存HTML报告到: {backup_path}")
                    
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    
                    # 验证文件
                    if os.path.exists(backup_path):
                        filesize = os.path.getsize(backup_path)
                        success_msg = f"HTML报告已成功保存至备用位置: {backup_path} (文件大小: {filesize} 字节)"
                        self.write_log(success_msg)
                        self._log_to_file(success_msg)
                        sys.stdout.write(f"\n[成功] HTML报告已保存至: \n{backup_path}\n")
                        sys.stdout.write("请使用浏览器打开查看交互式图表和数据\n")
                        sys.stdout.flush()
                        
                        # 尝试自动打开HTML报告
                        try:
                            import webbrowser
                            self._log_to_file("尝试使用webbrowser打开备用位置的HTML报告")
                            webbrowser.open(backup_path)
                            sys.stdout.write("已尝试自动打开浏览器查看报告\n")
                            sys.stdout.flush()
                        except Exception as browser_err:
                            self._log_to_file(f"自动打开浏览器失败: {browser_err}")
                            
                        return backup_path
                except Exception as backup_err:
                    self._log_to_file(f"保存HTML到备用位置 {location} 失败: {backup_err}")
            
            # 所有保存尝试都失败
            error_msg = "所有保存尝试均失败，无法生成HTML报告"
            self.write_log(error_msg)
            self._log_to_file(error_msg)
            sys.stdout.write(f"[ERROR] {error_msg}\n")
            sys.stdout.flush()
            return None
            
            # 尝试自动打开浏览器
            try:
                import webbrowser
                webbrowser.open(file_path)
                sys.stdout.write("已尝试自动打开浏览器查看报告\n")
                sys.stdout.flush()
            except:
                pass
                
            return file_path
        except Exception as e:
            self.write_log(f"生成HTML报告失败: {e}")
            import traceback
            self.write_log(traceback.format_exc())
            return None
    
    def force_update_all_factors(self):
        """强制更新所有因子"""
        sys.stdout.write("\n===== 强制更新所有因子 =====\n")
        sys.stdout.flush()
        
        if not self.am.inited:
            sys.stdout.write("历史数据尚未准备好，无法更新因子\n")
            sys.stdout.flush()
            return False
        
        # 获取当前K线时间
        if len(self.am.datetime_array) > 0:
            current_time = self.am.datetime_array[-1]
        else:
            from datetime import datetime
            current_time = datetime.now()
            
        # 强制更新因子
        self.write_log(f"手动触发因子更新，时间: {current_time}")
        if self._fetch_alpha_factors():
            # 更新因子历史记录
            for factor, value in self.factor_values.items():
                if factor in self.factor_history:
                    self.factor_history[factor].append(value)
            self.datetime_history.append(current_time)
            
            # 计算综合Alpha
            try:
                self.combined_alpha = self._calculate_combined_alpha()
                self.write_log(f"计算综合Alpha值: {self.combined_alpha}")
                
                # 生成交易信号
                self.signal = 1 if self.combined_alpha > self.threshold else 0
                self.signal_history.append(self.signal)
                self.write_log(f"生成交易信号: {self.signal} (1-买入, 0-卖出)")
                
                # 输出详细结果
                self._print_factor_details()
                
                sys.stdout.write("因子更新成功！\n")
                sys.stdout.write(f"综合Alpha: {self.combined_alpha:.6f}, 交易信号: {'买入' if self.signal == 1 else '卖出'}\n")
                sys.stdout.flush()
                return True
            except Exception as e:
                sys.stdout.write(f"计算综合Alpha时出错: {str(e)}\n")
                sys.stdout.flush()
                return False
        else:
            sys.stdout.write("因子更新失败\n")
            sys.stdout.flush()
            return False
    
    def _setup_log_file(self, strategy_name):
        """设置日志文件"""
        try:
            # 创建logs目录
            logs_dir = os.path.join(os.getcwd(), "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            # 生成日志文件名，格式为：logs/策略名_YYYYMMDD.log
            date_str = datetime.now().strftime("%Y%m%d")
            log_filename = f"{strategy_name}_{date_str}.log"
            self.log_filepath = os.path.join(logs_dir, log_filename)
            
            # 配置日志处理器 - 确保overwrite模式是'w'而非默认的'a'，清空之前的日志
            self.file_handler = logging.FileHandler(self.log_filepath, mode='w', encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s\t[%(levelname)s] %(message)s')
            self.file_handler.setFormatter(formatter)
            
            # 配置日志记录器
            self.logger = logging.getLogger(f"strategy_{strategy_name}")
            self.logger.setLevel(logging.INFO)
            
            # 清除之前的处理器
            for handler in self.logger.handlers[:]:
                self.logger.removeHandler(handler)
            
            # 确保logger的propagate设为False，避免重复日志
            self.logger.propagate = False    
                
            self.logger.addHandler(self.file_handler)
            
            # 在控制台输出日志文件路径
            sys.stdout.write(f"[INFO] 日志文件创建成功: {self.log_filepath}\n")
            sys.stdout.flush()
            
            # 使用print而不是write_log，避免在初始化过程中的循环调用
            print(f"[INFO] 日志文件创建成功: {self.log_filepath}")
            
            # 将首条日志消息直接写入日志文件
            self.logger.info(f"日志系统初始化成功，策略: {strategy_name}")
            
            # 写入一些基础信息以验证日志正常工作
            self.logger.info(f"交易品种: {self.vt_symbol}")
            self.logger.info(f"参数配置: 因子={self.alpha_factors}, 权重={self.factor_weights}, 阈值={self.threshold}")
            
            # 检查日志文件是否成功创建并写入
            try:
                file_size = os.path.getsize(self.log_filepath)
                sys.stdout.write(f"[INFO] 日志文件写入成功 (大小: {file_size} 字节)\n")
                sys.stdout.flush()
            except Exception as check_err:
                sys.stdout.write(f"[WARN] 日志文件写入检查失败: {check_err}\n")
                sys.stdout.flush()
            
        except Exception as e:
            # 使用print而不是write_log，避免在出现异常时的循环调用
            print(f"[ERROR] 日志文件设置失败: {str(e)}，将使用默认日志方式")
            sys.stdout.write(f"[ERROR] 日志文件设置失败详情: {traceback.format_exc()}\n")
            sys.stdout.flush()
    
    def _log_to_file(self, msg):
        """记录日志到文件"""
        try:
            if hasattr(self, 'logger') and self.logger:
                self.logger.info(msg)
                # 为了确保关键信息同时输出到控制台，提高可见性
                sys.stdout.write(f"[LOG] {msg}\n")
                sys.stdout.flush()
            else:
                # 如果日志记录器尚未创建，则使用默认日志方式或直接打印
                try:
                    self.write_log(f"文件日志未初始化，消息: {msg}")
                except:
                    # 如果write_log不可用，则直接打印
                    print(f"[INFO] 文件日志未初始化，消息: {msg}")
                    sys.stdout.write(f"[INFO] 文件日志未初始化，消息: {msg}\n")
                    sys.stdout.flush()
        except Exception as e:
            # 尝试使用write_log记录错误，如果不可用则打印
            try:
                self.write_log(f"日志记录失败: {str(e)}")
            except:
                print(f"[ERROR] 日志记录失败: {str(e)}")
                sys.stdout.write(f"[ERROR] 日志记录失败: {str(e)}\n")
                sys.stdout.flush()
    
    def _normalize_factor_value(self, value):
        """标准化因子值到[-1, 1]区间"""
        try:
            # 简单截断法
            if value > 3:
                return 1.0
            elif value < -3:
                return -1.0
            else:
                return value / 3.0  # 假设典型值范围是[-3, 3]
        except Exception as e:
            self.write_log(f"标准化因子值时出错: {str(e)}")
            return 0.0  # 出错时返回中性值
    
    def _print_factor_details(self):
        """输出因子详细信息，包括信号方向和强度"""
        self.write_log("\n=== 单因子分析 ===")
        for factor, value in sorted(self.factor_values.items()):
            # 标准化因子值
            normalized = self._normalize_factor_value(value)
            
            # 确定因子方向和强度
            direction = "看涨" if normalized > 0 else "看跌"
            strength = "强" if abs(normalized) > 0.7 else "中" if abs(normalized) > 0.3 else "弱"
            
            self.write_log(f"因子 {factor}: 原值={value:.6f}, 标准化={normalized:.4f}, 信号={strength}{direction}")
        
        self.write_log("=== 单因子分析结束 ===\n")
    
    def _test_directory_writeable(self, dir_path):
        """测试目录是否可写入"""
        try:
            import os
            import tempfile
            
            # 如果目录不存在，尝试创建
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path)
                    self.write_log(f"成功创建目录: {dir_path}")
                except Exception as e:
                    self.write_log(f"创建目录失败: {e}")
                    return False
            
            # 测试是否可写入临时文件
            try:
                test_file = os.path.join(dir_path, "test_write.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                self.write_log(f"目录可写入: {dir_path}")
                return True
            except Exception as e:
                self.write_log(f"目录写入测试失败: {e}")
                return False
        except Exception as e:
            self.write_log(f"测试目录可写入性时出错: {e}")
            return False

    def _check_system_environment(self):
        """检查系统环境，确保文件读写正常"""
        try:
            import os
            import tempfile
            import platform
            
            # 输出基本系统信息
            sys_info = f"操作系统: {platform.system()} {platform.release()}"
            python_info = f"Python版本: {platform.python_version()}"
            
            sys.stdout.write(f"[系统] {sys_info}\n")
            sys.stdout.write(f"[系统] {python_info}\n")
            
            # 检查当前目录
            current_dir = os.getcwd()
            sys.stdout.write(f"[系统] 当前工作目录: {current_dir}\n")
            
            # 检查临时目录是否可写
            temp_dir = tempfile.gettempdir()
            try:
                test_file = os.path.join(temp_dir, "vn_py_test.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                sys.stdout.write(f"[系统] 临时目录可写: {temp_dir}\n")
            except Exception as temp_err:
                sys.stdout.write(f"[警告] 临时目录写入测试失败: {temp_err}\n")
            
            # 检查用户主目录是否可写
            home_dir = os.path.expanduser("~")
            try:
                test_file = os.path.join(home_dir, "vn_py_test.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                sys.stdout.write(f"[系统] 用户目录可写: {home_dir}\n")
            except Exception as home_err:
                sys.stdout.write(f"[警告] 用户目录写入测试失败: {home_err}\n")
                
            # 尝试创建常用目录
            test_dirs = [
                os.path.join(current_dir, "factor_data"),
                os.path.join(current_dir, "factor_reports"),
                os.path.join(current_dir, "logs")
            ]
            
            for test_dir in test_dirs:
                try:
                    if not os.path.exists(test_dir):
                        os.makedirs(test_dir)
                        sys.stdout.write(f"[系统] 创建目录: {test_dir}\n")
                    else:
                        sys.stdout.write(f"[系统] 目录已存在: {test_dir}\n")
                except Exception as dir_err:
                    sys.stdout.write(f"[警告] 创建目录失败 {test_dir}: {dir_err}\n")
            
            sys.stdout.write("[系统] 环境检查完成\n\n")
            sys.stdout.flush()
        except Exception as e:
            sys.stdout.write(f"[错误] 系统环境检查过程中发生异常: {e}\n")
            sys.stdout.flush()