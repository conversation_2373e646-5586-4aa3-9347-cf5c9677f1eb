from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)

import numpy as np
import pandas as pd
import datetime as dt

# 简化的因子导入
try:
    from .factor_alpha101 import Alphas
    FACTOR_MODULE_LOADED = True
except ImportError:
    try:
        import factor_alpha101
        Alphas = factor_alpha101.Alphas
        FACTOR_MODULE_LOADED = True
    except ImportError:
        FACTOR_MODULE_LOADED = False
        print("警告：无法导入factor_alpha101模块，将使用模拟因子")

        # 创建模拟的Alphas类
        class MockAlphas:
            def __init__(self, data):
                self.data = data

            def alpha001(self):
                """模拟alpha001因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                returns = self.data['S_DQ_CLOSE'].pct_change().fillna(0)
                return returns.rolling(5).mean()

            def alpha015(self):
                """模拟alpha015因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                high = self.data['S_DQ_HIGH']
                close = self.data['S_DQ_CLOSE']
                return (high - close).rolling(3).mean()

            def alpha030(self):
                """模拟alpha030因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                volume = self.data['S_DQ_VOLUME']
                return volume.rolling(10).mean()

            def alpha045(self):
                """模拟alpha045因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                close = self.data['S_DQ_CLOSE']
                return close.rolling(5).std()

            def alpha060(self):
                """模拟alpha060因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                volume = self.data['S_DQ_VOLUME']
                close = self.data['S_DQ_CLOSE']
                return (volume * close).rolling(7).mean()

        Alphas = MockAlphas


class AlphaStrategy(CtaTemplate):
    """基于Alpha101因子的多因子量化交易策略"""

    author = "AlphaTrader"

    # 策略参数 - Alpha101因子配置
    alpha_factors = ["alpha001", "alpha015", "alpha030", "alpha045", "alpha060"]  # 选择的Alpha因子
    factor_weights = [0.2, 0.2, 0.2, 0.2, 0.2]  # 各因子权重，和为1
    signal_threshold = 0.1       # 信号阈值（降低以便更容易触发交易）
    lookback_days = 30           # 获取因子计算的历史数据天数
    risk_percent = 0.02          # 风险比例（资金的百分比）
    atr_period = 20              # ATR计算周期
    atr_multiplier = 2.0         # ATR止损倍数

    # 交易参数
    price_offset = 0.002         # 限价单价格偏移（0.2%）
    max_position = 5             # 最大持仓手数

    # 调试参数
    debug_mode = True            # 是否开启调试模式
    debug_interval = 10          # 调试信息输出间隔（K线数）
    force_trade_test = False     # 强制交易测试（忽略信号，定期交易）

    # 策略变量
    combined_alpha = 0.0         # 综合Alpha值
    signal = 0                   # 交易信号 (1:买入, -1:卖出, 0:无信号)
    last_factor_time = None      # 上次计算因子的时间
    current_atr = 0.0            # 当前ATR值

    # 参数列表
    parameters = [
        "alpha_factors", "factor_weights", "signal_threshold", "lookback_days",
        "risk_percent", "atr_period", "atr_multiplier", "price_offset", "max_position"
    ]

    # 变量列表
    variables = [
        "combined_alpha", "signal", "current_atr"
    ]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """策略初始化"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        # 处理alpha_factors参数
        if "alpha_factors" in setting:
            if isinstance(setting["alpha_factors"], list):
                # 确保列表中的元素都是字符串
                self.alpha_factors = [str(factor).strip().strip("'\"") for factor in setting["alpha_factors"]]
            elif isinstance(setting["alpha_factors"], str):
                # 处理字符串格式的因子列表
                cleaned = setting["alpha_factors"].strip("[]() ")
                if cleaned:
                    self.alpha_factors = [x.strip().strip("'\"") for x in cleaned.split(",") if x.strip()]
                else:
                    self.write_log("因子字符串为空，使用默认因子")
                    # 保持默认值不变
            else:
                self.write_log(f"因子参数类型不支持: {type(setting['alpha_factors'])}，使用默认因子")

        # 处理factor_weights参数
        if "factor_weights" in setting:
            if isinstance(setting["factor_weights"], list):
                # 处理列表中可能包含字符串的情况
                try:
                    self.factor_weights = [float(w) for w in setting["factor_weights"]]
                except (ValueError, TypeError):
                    # 如果列表中有非数字元素，尝试字符串解析
                    self.write_log(f"权重列表包含非数字元素，尝试字符串解析: {setting['factor_weights']}")
                    weight_str = str(setting["factor_weights"]).strip("[]() ")
                    try:
                        parts = weight_str.split(",")
                        self.factor_weights = [float(part.strip().strip("'\"")) for part in parts if part.strip()]
                    except ValueError:
                        self.write_log(f"无法解析权重，使用默认等权重")
                        self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
            elif isinstance(setting["factor_weights"], str):
                cleaned = setting["factor_weights"].strip("[]() ")
                if cleaned:
                    try:
                        parts = cleaned.split(",")
                        self.factor_weights = [float(part.strip().strip("'\"")) for part in parts if part.strip()]
                    except ValueError:
                        self.write_log(f"无法解析权重: {cleaned}，使用默认等权重")
                        self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
                else:
                    self.write_log("权重字符串为空，使用默认等权重")
                    self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
            else:
                # 其他类型，使用默认等权重
                self.write_log(f"权重参数类型不支持: {type(setting['factor_weights'])}，使用默认等权重")
                self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)

        # 确保权重与因子数量相等并标准化
        if len(self.factor_weights) != len(self.alpha_factors):
            self.write_log(f"权重数量({len(self.factor_weights)})与因子数量({len(self.alpha_factors)})不匹配，使用等权重")
            self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)

        # 标准化权重，确保和为1
        weight_sum = sum(self.factor_weights)
        if weight_sum > 0:
            self.factor_weights = [w/weight_sum for w in self.factor_weights]

        self.write_log(f"使用的因子: {self.alpha_factors}")
        self.write_log(f"因子权重: {self.factor_weights}")

        # 初始化数据管理器
        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager()

        # 调试相关变量
        self.bar_count = 0           # K线计数器
        self.debug_info = {}         # 调试信息存储
        self.last_debug_time = None  # 上次调试输出时间

        # 初始化因子数据存储
        self.factor_values = {}
        self.factor_history = []

    def on_init(self):
        """策略初始化完成回调"""
        self.write_log("策略初始化")
        # 加载足够的历史数据
        self.load_bar(self.lookback_days + 10)

    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")

        # 预检查历史数据可用性
        if self.am.inited:
            self.write_log(f"ArrayManager已初始化，数组长度: {len(self.am.close_array)}")
        else:
            self.write_log("警告：ArrayManager未初始化，可能影响因子计算")

        self.put_event()

    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        self.put_event()

    def on_tick(self, tick: TickData):
        """Tick数据更新回调"""
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData):
        """K线数据更新回调 - 核心交易逻辑"""
        # 更新数据到数组管理器
        self.am.update_bar(bar)
        if not self.am.inited:
            return

        # 取消所有订单
        self.cancel_all()

        # 检查数据是否足够
        if len(self.am.close_array) < self.lookback_days:
            self.write_log(f"历史数据不足{self.lookback_days}条，暂不生成交易信号")
            return

        # 检查是否需要更新Alpha因子
        current_time = bar.datetime
        if self._should_update_factors(current_time):
            self.write_log(f"开始更新Alpha因子，当前时间: {current_time}")
            try:
                self._calculate_alpha_factors()
                self.last_factor_time = current_time
                self.write_log("Alpha因子计算完成")
            except Exception as e:
                self.write_log(f"Alpha因子计算失败: {e}")
                self.write_log(f"详细错误: {str(e)}")
                return

        # 计算综合Alpha信号
        if self.factor_values:
            self.combined_alpha = self._calculate_combined_alpha()
            self.write_log(f"综合Alpha值: {self.combined_alpha:.6f}")

            self._generate_trading_signal()
            self.write_log(f"生成交易信号: {self.signal}")

            # 执行交易策略
            self._execute_trading_strategy(bar)
        else:
            self.write_log("警告：因子值为空，无法生成交易信号")

        # 调试信息输出
        self._debug_strategy_status(bar)

        # 更新UI
        self.put_event()

    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        pass

    def on_trade(self, trade: TradeData):
        """成交回报回调"""
        direction_text = '买入' if trade.direction.value == '多' else '卖出'
        self.write_log(f"{direction_text}{trade.volume}手，价格:{trade.price}")

        # 记录当前因子值与交易关系
        if hasattr(self, 'factor_values') and self.factor_values:
            factor_info = "; ".join([f"{factor}: {value:.4f}" for factor, value in self.factor_values.items()])
            self.write_log(f"交易时因子值: {factor_info}")
            self.write_log(f"综合Alpha: {self.combined_alpha:.4f}")

        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """停止单状态更新回调"""
        pass

    def _should_update_factors(self, current_time):
        """判断是否需要更新因子"""
        if self.last_factor_time is None:
            return True

        # 每天更新一次因子
        if current_time.date() != self.last_factor_time.date():
            return True

        return False

    def _get_history_dataframe(self):
        """获取历史数据并转换为Alpha101所需的DataFrame格式"""
        try:
            # 从ArrayManager获取历史数据
            if not self.am.inited or len(self.am.close_array) < self.lookback_days:
                return None

            # 构建DataFrame
            data_length = min(self.lookback_days, len(self.am.close_array))

            # 转换为Alpha101期望的格式
            df = pd.DataFrame({
                'S_DQ_OPEN': self.am.open_array[-data_length:],
                'S_DQ_HIGH': self.am.high_array[-data_length:],
                'S_DQ_LOW': self.am.low_array[-data_length:],
                'S_DQ_CLOSE': self.am.close_array[-data_length:],
                'S_DQ_VOLUME': self.am.volume_array[-data_length:],
            })

            # 计算收益率和成交额（Alpha101需要）
            df['S_DQ_PCTCHANGE'] = df['S_DQ_CLOSE'].pct_change().fillna(0)
            # 假设成交额 = 收盘价 * 成交量（简化处理）
            df['S_DQ_AMOUNT'] = df['S_DQ_CLOSE'] * df['S_DQ_VOLUME'] / 1000  # 转换为千元单位

            # 添加索引
            df.index = range(len(df))

            return df

        except Exception as e:
            self.write_log(f"获取历史数据失败: {e}")
            return None

    def _calculate_alpha_factors(self):
        """计算Alpha101因子"""
        try:
            # 获取历史数据
            history_data = self._get_history_dataframe()
            if history_data is None or len(history_data) < self.lookback_days:
                raise ValueError(f"历史数据不足，需要{self.lookback_days}条，实际{len(history_data) if history_data is not None else 0}条")

            self.write_log(f"开始计算Alpha因子，使用{len(history_data)}条历史数据")
            self.write_log(f"数据列: {list(history_data.columns)}")
            self.write_log(f"数据样本: 最新收盘价={history_data['S_DQ_CLOSE'].iloc[-1]:.2f}, 最新成交量={history_data['S_DQ_VOLUME'].iloc[-1]}")

            # 创建Alphas实例
            try:
                alphas_calculator = Alphas(history_data)
                self.write_log("成功创建Alpha101计算器")
            except Exception as e:
                self.write_log(f"创建Alpha101计算器失败: {e}")
                raise

            # 清空之前的因子值
            self.factor_values = {}

            # 逐个计算选定的Alpha因子
            for factor_name in self.alpha_factors:
                try:
                    if hasattr(alphas_calculator, factor_name):
                        factor_func = getattr(alphas_calculator, factor_name)
                        factor_value = factor_func()

                        # 获取最新的因子值（通常是最后一个值）
                        if isinstance(factor_value, pd.Series):
                            latest_value = factor_value.iloc[-1] if len(factor_value) > 0 else 0
                        elif isinstance(factor_value, pd.DataFrame):
                            # 如果返回DataFrame，取最后一行的第一列
                            latest_value = factor_value.iloc[-1, 0] if len(factor_value) > 0 else 0
                        elif isinstance(factor_value, (int, float)):
                            latest_value = factor_value
                        else:
                            latest_value = 0

                        # 检查因子值是否有效
                        if pd.isna(latest_value) or np.isinf(latest_value):
                            self.write_log(f"因子{factor_name}计算结果无效: {latest_value}")
                            latest_value = 0

                        self.factor_values[factor_name] = float(latest_value)
                        self.write_log(f"因子{factor_name}: {latest_value:.6f}")

                    else:
                        self.write_log(f"警告：Alpha101计算器中未找到{factor_name}方法")
                        self.factor_values[factor_name] = 0

                except Exception as e:
                    self.write_log(f"计算因子{factor_name}时出错: {e}")
                    self.factor_values[factor_name] = 0

            # 记录因子历史
            factor_record = {
                'datetime': dt.datetime.now(),
                'factors': self.factor_values.copy(),
                'combined_alpha': self.combined_alpha
            }
            self.factor_history.append(factor_record)

            # 保持历史记录不超过100条
            if len(self.factor_history) > 100:
                self.factor_history = self.factor_history[-100:]

        except Exception as e:
            self.write_log(f"Alpha因子计算失败: {e}")
            raise

    def _calculate_combined_alpha(self):
        """计算综合Alpha值"""
        if not self.factor_values:
            return 0.0

        # 标准化处理各个因子值
        normalized_factors = {}
        for factor, value in self.factor_values.items():
            if abs(value) > 1e-10:
                # 简单的标准化：限制在[-1, 1]区间
                normalized_factors[factor] = max(min(value/abs(value), 1), -1)
            else:
                normalized_factors[factor] = 0

        # 按权重加权平均
        combined = 0
        for i, factor in enumerate(self.alpha_factors):
            if factor in normalized_factors:
                combined += normalized_factors[factor] * self.factor_weights[i]

        return combined

    def _generate_trading_signal(self):
        """生成交易信号"""
        if self.combined_alpha > self.signal_threshold:
            self.signal = 1  # 买入信号
        elif self.combined_alpha < -self.signal_threshold:
            self.signal = -1  # 卖出信号
        else:
            self.signal = 0  # 无信号

        self.write_log(f"综合Alpha: {self.combined_alpha:.4f}, 信号: {self.signal}")

    def _execute_trading_strategy(self, bar: BarData):
        """执行交易策略"""
        try:
            # 计算ATR用于风险控制
            self.current_atr = self.am.atr(self.atr_period)
            if self.current_atr <= 0:
                self.write_log("ATR计算无效，跳过交易")
                return

            self.write_log(f"当前ATR: {self.current_atr:.4f}")

            # 计算仓位大小（基于风险控制）
            account_balance = 100000  # 假设账户余额，实际应从账户信息获取
            risk_amount = account_balance * self.risk_percent
            position_size = min(int(risk_amount / (self.current_atr * self.atr_multiplier)), self.max_position)
            position_size = max(position_size, 1)  # 至少1手

            current_price = bar.close_price
            self.write_log(f"当前价格: {current_price:.2f}, 当前持仓: {self.pos}, 计算仓位: {position_size}")

            # 执行交易逻辑 - 使用市价单以确保成交
            if self.signal == 1 and self.pos <= 0:  # 买入信号且无多头持仓
                # 平空仓
                if self.pos < 0:
                    self.cover(current_price, abs(self.pos))  # 使用市价
                    self.write_log(f"平空仓 {abs(self.pos)} 手，市价: {current_price:.2f}")

                # 开多仓
                self.buy(current_price, position_size)  # 使用市价
                self.write_log(f"开多仓 {position_size} 手，市价: {current_price:.2f}")

            elif self.signal == -1 and self.pos >= 0:  # 卖出信号且无空头持仓
                # 平多仓
                if self.pos > 0:
                    self.sell(current_price, self.pos)  # 使用市价
                    self.write_log(f"平多仓 {self.pos} 手，市价: {current_price:.2f}")

                # 开空仓
                self.short(current_price, position_size)  # 使用市价
                self.write_log(f"开空仓 {position_size} 手，市价: {current_price:.2f}")

            # 止损逻辑
            if self.pos > 0:  # 多头持仓
                stop_price = current_price - self.current_atr * self.atr_multiplier
                if current_price <= stop_price:
                    self.sell(current_price, self.pos)
                    self.write_log(f"多头止损，价格: {current_price:.2f}")

            elif self.pos < 0:  # 空头持仓
                stop_price = current_price + self.current_atr * self.atr_multiplier
                if current_price >= stop_price:
                    self.cover(current_price, abs(self.pos))
                    self.write_log(f"空头止损，价格: {current_price:.2f}")

        except Exception as e:
            self.write_log(f"执行交易策略时出错: {e}")

    def _debug_strategy_status(self, bar: BarData):
        """调试策略状态 - 诊断为什么没有交易信号"""
        if not self.debug_mode:
            return

        self.bar_count += 1

        # 每隔一定K线数输出调试信息
        if self.bar_count % self.debug_interval == 0:
            self.write_log("=" * 50)
            self.write_log(f"🔍 调试信息 - 第{self.bar_count}根K线")
            self.write_log("=" * 50)

            # 1. 检查历史数据
            data_length = len(self.am.close_array) if self.am.inited else 0
            self.write_log(f"📊 数据检查:")
            self.write_log(f"   ArrayManager初始化: {'✓' if self.am.inited else '✗'}")
            self.write_log(f"   历史数据长度: {data_length}")
            self.write_log(f"   需要数据长度: {self.lookback_days}")
            self.write_log(f"   数据充足: {'✓' if data_length >= self.lookback_days else '✗'}")

            if self.am.inited and data_length > 0:
                self.write_log(f"   最新价格: {self.am.close[-1]:.2f}")
                self.write_log(f"   价格范围: {min(self.am.close_array):.2f} - {max(self.am.close_array):.2f}")

            # 2. 检查因子计算
            self.write_log(f"🧮 因子检查:")
            self.write_log(f"   配置因子: {self.alpha_factors}")
            self.write_log(f"   因子权重: {[f'{w:.3f}' for w in self.factor_weights]}")

            if hasattr(self, 'factor_values') and self.factor_values:
                self.write_log(f"   计算成功的因子数: {len(self.factor_values)}")
                for factor, value in self.factor_values.items():
                    self.write_log(f"   {factor}: {value:.6f}")
            else:
                self.write_log(f"   ✗ 因子值为空或未计算")

            # 3. 检查信号生成
            self.write_log(f"📡 信号检查:")
            if hasattr(self, 'combined_alpha'):
                self.write_log(f"   综合Alpha: {self.combined_alpha:.6f}")
                self.write_log(f"   信号阈值: ±{self.signal_threshold}")
                self.write_log(f"   当前信号: {getattr(self, 'signal', 'N/A')}")

                # 分析信号强度
                if abs(self.combined_alpha) < self.signal_threshold:
                    self.write_log(f"   ⚠ 信号强度不足 (|{self.combined_alpha:.6f}| < {self.signal_threshold})")
                else:
                    signal_type = "买入" if self.combined_alpha > 0 else "卖出"
                    self.write_log(f"   ✓ 信号强度充足，应生成{signal_type}信号")
            else:
                self.write_log(f"   ✗ 综合Alpha未计算")

            self.write_log("=" * 50)
