{"name": "@jupyterlab/logconsole-extension", "version": "4.3.6", "description": "JupyterLab - Log Console Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -w --listEmittedFiles"}, "dependencies": {"@jupyterlab/application": "^4.3.6", "@jupyterlab/apputils": "^4.4.6", "@jupyterlab/coreutils": "^6.3.6", "@jupyterlab/logconsole": "^4.3.6", "@jupyterlab/rendermime": "^4.3.6", "@jupyterlab/settingregistry": "^4.3.6", "@jupyterlab/statusbar": "^4.3.6", "@jupyterlab/translation": "^4.3.6", "@jupyterlab/ui-components": "^4.3.6", "@lumino/coreutils": "^2.2.0", "@lumino/signaling": "^2.1.3", "@lumino/widgets": "^2.5.0", "react": "^18.2.0"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}