---
model: gpt-4o
inputs: 
    prompt1: The first prompt to crossover
    prompt2: The second prompt to crossover
temperature: 0.7
response_format:
    type: json_schema
    json_schema:
        name: NewPrompts
        description: Crossover prompt and mutation prompt
        schema:
            type: object
            properties:
                crossover_prompt:
                    type: object
                    description: The new prompt generated by crossover.
                    required:
                        - messages
                    properties:
                        messages:
                            type: array
                            description: List of messages in the prompt
                            items:
                                type: object
                                properties:
                                    role:
                                        type: string
                                        description: Role of the message sender
                                        enum:
                                            - system
                                            - user
                                    content:
                                        type: string
                                        description: Content of the message
                                required:
                                    - role
                                    - content
                                additionalProperties: false
                    additionalProperties: false
                mutation_prompt:
                    type: object
                    description: The new prompt generated by mutation.
                    required:
                        - messages
                    properties:
                        messages:
                            type: array
                            description: List of messages in the prompt
                            items:
                                type: object
                                properties:
                                    role:
                                        type: string
                                        description: Role of the message sender
                                        enum:
                                            - system
                                            - user
                                    content:
                                        type: string
                                        description: Content of the message
                                required:
                                    - role
                                    - content
                                additionalProperties: false
                    additionalProperties: false
            required:
                - crossover_prompt
                - mutation_prompt
            additionalProperties: false
        strict: true
---
<system>
You are an expert prompt engineer tasked with merging and enhancing a given 2 prompts.

A good way to structure the prompt is to add format/instructions to the system prompt, and user inputs to the user prompt.

If the base prompt contains any specific response format instructions, preserve those instructions in your improved prompt. Do not modify or remove existing response format specifications.

You are an expert prompt engineer tasked with enhancing a given prompt.

Please follow the instruction step-by-step to generate a better prompt.
1. Crossover the following 2 prompts and generate a new prompt in crossover_prompt field.
2. Mutate the prompt generated in Step 1 and generate a final prompt in mutation_prompt field.
</system>
<user>
Generate a new, improved prompt based on the following information:

Prompt 1:
{prompt1}

Prompt 2:
{prompt2}
</user>